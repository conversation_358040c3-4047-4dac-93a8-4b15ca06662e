import { MeteoraEventParser } from '../parsers/meteora/meteoraEventParser';
import { DataHandler } from '../dataHandler';
import { MessageBroker } from '../services/messageBroker';
import { SolanaDexTrade, SolanaDexNewPool } from '../types';

/**
 * Meteora 集成测试
 * 测试从事件解析到数据投递的完整流程
 */
class MeteoraIntegrationTest {
    private parser: MeteoraEventParser;
    private dataHandler: DataHandler;
    private broker: MessageBroker;

    constructor() {
        // 支持的 Meteora 程序 ID
        const supportedPrograms = [
            'LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo', // Meteora DLMM
            'Eo7WjKq67rjJQSZxS6z3YkapzY3eMj6Xy8X5EQVn5UaB', // Meteora DAMM V1
        ];
        
        this.parser = new MeteoraEventParser(supportedPrograms);
        this.dataHandler = new DataHandler();
        this.broker = MessageBroker.getInstance();
    }

    /**
     * 初始化测试环境
     */
    async setup(): Promise<void> {
        console.log('🚀 初始化 Meteora 集成测试环境...');
        
        // 连接消息代理
        await this.broker.connect();
        console.log('✅ 消息代理连接成功');
    }

    /**
     * 测试模拟交易数据
     */
    async testMockTradeData(): Promise<void> {
        console.log('\n📊 测试模拟交易数据...');
        
        // 模拟 Meteora DLMM Swap 交易
        const mockTrade: SolanaDexTrade = {
            dexName: 'Meteora DLMM',
            poolAddress: 'LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo',
            txHash: 'test_tx_hash_123',
            txType: 'buy',
            slot: 123456789,
            instructionIndex: '0_0',
            timestamp: Math.floor(Date.now() / 1000),
            traderAddress: 'trader_address_123',
            token0Address: 'So11111111111111111111111111111111111111112', // SOL
            token1Address: 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v', // USDC
            token0Amount: '1000000000', // 1 SOL (9 decimals)
            token1Amount: '100000000', // 100 USDC (6 decimals)
            token0Reserve: '50000000000', // 50 SOL
            token1Reserve: '5000000000', // 5000 USDC
            dexSource: 'Event-Meteora DLMM',
        };

        // 模拟新池创建
        const mockNewPool: SolanaDexNewPool = {
            dexName: 'Meteora DLMM',
            pairAddress: 'new_pool_address_123',
            token0Address: 'So11111111111111111111111111111111111111112', // SOL
            token1Address: 'new_token_address_123',
            creationTxHash: 'creation_tx_hash_123',
            blockNumber: 123456789,
            timestamp: Math.floor(Date.now() / 1000),
        };

        try {
            // 测试交易处理
            console.log('处理模拟交易...');
            await this.dataHandler.handleTrades([mockTrade]);
            console.log('✅ 交易处理成功');

            // 测试新池处理
            console.log('处理模拟新池...');
            await this.dataHandler.handleNewPools([mockNewPool]);
            console.log('✅ 新池处理成功');

        } catch (error) {
            console.error('❌ 测试失败:', error);
            throw error;
        }
    }

    /**
     * 测试数据验证
     */
    testDataValidation(): void {
        console.log('\n🔍 测试数据验证...');
        
        // 测试有效交易
        const validTrade: SolanaDexTrade = {
            dexName: 'Meteora DLMM',
            poolAddress: 'valid_pool',
            txHash: 'valid_tx',
            txType: 'buy',
            slot: 123,
            instructionIndex: '0',
            timestamp: Date.now(),
            traderAddress: 'valid_trader',
            token0Address: 'token0',
            token1Address: 'token1',
            token0Amount: '1000',
            token1Amount: '2000',
            token0Reserve: '10000',
            token1Reserve: '20000',
            dexSource: 'test',
        };

        // 测试无效交易（金额为0）
        const invalidTrade: SolanaDexTrade = {
            ...validTrade,
            token0Amount: '0',
        };

        console.log('有效交易验证:', this.isValidTrade(validTrade) ? '✅ 通过' : '❌ 失败');
        console.log('无效交易验证:', !this.isValidTrade(invalidTrade) ? '✅ 通过' : '❌ 失败');
    }

    /**
     * 验证交易是否有效（复制自 DataHandler 的私有方法）
     */
    private isValidTrade(trade: SolanaDexTrade): boolean {
        return trade.token0Amount !== "0" && trade.token1Amount !== "0" && !!trade.txType;
    }

    /**
     * 运行完整测试套件
     */
    async runTests(): Promise<void> {
        try {
            await this.setup();
            
            this.testDataValidation();
            await this.testMockTradeData();
            
            console.log('\n🎉 所有测试通过！');
            
        } catch (error) {
            console.error('\n💥 测试失败:', error);
            process.exit(1);
        } finally {
            // 清理资源
            await this.broker.close();
            console.log('🧹 测试环境清理完成');
        }
    }

    /**
     * 显示测试统计信息
     */
    showTestStats(): void {
        console.log('\n📈 测试统计信息:');
        console.log('- 支持的 DEX: Meteora DLMM, Meteora DAMM V1');
        console.log('- 支持的事件类型: Swap, AddLiquidity, RemoveLiquidity, InitializeLbPair');
        console.log('- Kafka Topics: 交易入库, 新池入库, 流动性入库, ES更新');
        console.log('- WebSocket Topics: 实时交易推送, Pump交易推送, 新池推送');
        console.log('- 数据验证: 交易金额验证, 异常交易检测');
    }
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
    const test = new MeteoraIntegrationTest();
    
    console.log('🧪 Meteora 数据投递集成测试');
    console.log('=====================================');
    
    test.showTestStats();
    test.runTests().catch(console.error);
}

export { MeteoraIntegrationTest };

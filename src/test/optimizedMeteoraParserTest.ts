import { MeteoraEventParser } from '../parsers/meteora/meteoraEventParser';
import { ConfirmedTransaction, SolanaDexTrade, SolanaDexNewPool } from '../types';

/**
 * 优化后的 Meteora 解析器测试
 * 验证事件数据最大化利用的效果
 */
class OptimizedMeteoraParserTest {
    private parser: MeteoraEventParser;

    constructor() {
        // 支持的 Meteora 程序 ID
        const supportedPrograms = [
            'LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo', // Meteora DLMM
            'Eo7WjKq67rjJQSZxS6z3YkapzY3eMj6Xy8X5EQVn5UaB', // Meteora DAMM V1
        ];
        
        this.parser = new MeteoraEventParser(supportedPrograms);
    }

    /**
     * 测试 DLMM Swap 事件优化
     */
    async testDLMMSwapOptimization(): Promise<void> {
        console.log('\n🧪 测试 DLMM Swap 事件优化...');
        
        // 模拟 DLMM Swap 交易数据
        const mockDLMMTransaction = this.createMockDLMMSwapTransaction();
        
        try {
            const results = await this.parser.parseTransaction(mockDLMMTransaction);
            
            if (results.length > 0) {
                const trade = results[0] as SolanaDexTrade;
                console.log('✅ DLMM Swap 解析成功:');
                console.log(`  - 池子地址: ${trade.poolAddress}`);
                console.log(`  - 交易者: ${trade.traderAddress}`);
                console.log(`  - 交易类型: ${trade.txType}`);
                console.log(`  - Token0 数量: ${trade.token0Amount}`);
                console.log(`  - Token1 数量: ${trade.token1Amount}`);
                console.log(`  - 数据来源: ${trade.dexSource}`);
                
                // 验证数据完整性
                this.validateTradeData(trade, 'DLMM');
            } else {
                console.log('❌ DLMM Swap 解析失败');
            }
        } catch (error) {
            console.error('❌ DLMM Swap 测试失败:', error);
        }
    }

    /**
     * 测试 DLMM 新池创建事件优化
     */
    async testDLMMNewPoolOptimization(): Promise<void> {
        console.log('\n🧪 测试 DLMM 新池创建事件优化...');
        
        const mockDLMMNewPoolTransaction = this.createMockDLMMNewPoolTransaction();
        
        try {
            const results = await this.parser.parseTransaction(mockDLMMNewPoolTransaction);
            
            if (results.length > 0) {
                const newPool = results[0] as SolanaDexNewPool;
                console.log('✅ DLMM 新池解析成功:');
                console.log(`  - 池子地址: ${newPool.pairAddress}`);
                console.log(`  - Token0: ${newPool.token0Address}`);
                console.log(`  - Token1: ${newPool.token1Address}`);
                console.log(`  - 创建交易: ${newPool.creationTxHash}`);
                
                // 验证代币地址是否从事件中正确获取
                if (newPool.token0Address !== 'unknown' && newPool.token1Address !== 'unknown') {
                    console.log('✅ 代币地址从事件中成功获取');
                } else {
                    console.log('⚠️ 代币地址未能从事件中获取');
                }
            } else {
                console.log('❌ DLMM 新池解析失败');
            }
        } catch (error) {
            console.error('❌ DLMM 新池测试失败:', error);
        }
    }

    /**
     * 测试数据质量统计
     */
    async testDataQualityStats(): Promise<void> {
        console.log('\n📊 测试数据质量统计...');
        
        // 重置统计
        this.parser.resetDataQualityStats();
        
        // 解析多个交易
        const transactions = [
            this.createMockDLMMSwapTransaction(),
            this.createMockDLMMNewPoolTransaction(),
            this.createMockDAMMV1SwapTransaction()
        ];
        
        for (const transaction of transactions) {
            try {
                await this.parser.parseTransaction(transaction);
            } catch (error) {
                console.log('解析交易时出错:', error);
            }
        }
        
        // 获取统计信息
        const stats = this.parser.getDataQualityStats();
        console.log('📈 数据质量统计:');
        console.log(`  - 总解析数: ${stats.totalParsed}`);
        console.log(`  - DLMM 事件使用: ${stats.dlmmEventUsage}`);
        console.log(`  - DAMM V1 事件使用: ${stats.dammV1EventUsage}`);
        console.log(`  - 指令回退次数: ${stats.instructionFallback}`);
        
        if (stats.totalParsed > 0) {
            const dlmmRate = (stats.dlmmEventUsage / stats.totalParsed * 100).toFixed(1);
            const dammRate = (stats.dammV1EventUsage / stats.totalParsed * 100).toFixed(1);
            console.log(`  - DLMM 事件使用率: ${dlmmRate}%`);
            console.log(`  - DAMM V1 事件使用率: ${dammRate}%`);
        }
    }

    /**
     * 验证交易数据完整性
     */
    private validateTradeData(trade: SolanaDexTrade, protocol: string): void {
        const issues: string[] = [];
        
        if (trade.poolAddress === 'unknown') issues.push('池子地址未知');
        if (trade.traderAddress === 'unknown') issues.push('交易者地址未知');
        if (trade.token0Amount === '0') issues.push('Token0 数量为0');
        if (trade.token1Amount === '0') issues.push('Token1 数量为0');
        if (!trade.txType) issues.push('交易类型未定义');
        
        if (issues.length === 0) {
            console.log(`✅ ${protocol} 数据完整性验证通过`);
        } else {
            console.log(`⚠️ ${protocol} 数据完整性问题:`, issues);
        }
    }

    /**
     * 创建模拟的 DLMM Swap 交易
     */
    private createMockDLMMSwapTransaction(): ConfirmedTransaction {
        return {
            slot: *********,
            transaction: {
                meta: {
                    err: null,
                    innerInstructions: [
                        {
                            index: 0,
                            instructions: [
                                {
                                    programIdIndex: 1,
                                    accounts: [0], // 事件 PDA 账户
                                    data: new Uint8Array([/* 模拟 DLMM Swap 事件数据 */])
                                }
                            ]
                        }
                    ],
                    postTokenBalances: [
                        {
                            accountIndex: 2,
                            mint: 'So11111111111111111111111111111111111111112', // SOL
                            owner: 'dlmm_pool_address_123',
                            uiTokenAmount: {
                                amount: '***********',
                                decimals: 9,
                                uiAmount: 50.0,
                                uiAmountString: '50.0'
                            }
                        },
                        {
                            accountIndex: 3,
                            mint: 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v', // USDC
                            owner: 'dlmm_pool_address_123',
                            uiTokenAmount: {
                                amount: '**********',
                                decimals: 6,
                                uiAmount: 5000.0,
                                uiAmountString: '5000.0'
                            }
                        }
                    ],
                    preTokenBalances: []
                },
                transaction: {
                    message: {
                        accountKeys: [
                            'dlmm_pool_address_123',                              // 0: 池子地址
                            'LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo',        // 1: DLMM 程序
                            'token0_vault_address',                               // 2: Token0 Vault
                            'token1_vault_address',                               // 3: Token1 Vault
                            'user_address_123',                                   // 4: 用户地址
                        ],
                        instructions: [
                            {
                                programIdIndex: 1,
                                accounts: [0, 1, 2, 3, 4],
                                data: new Uint8Array([/* 指令数据 */])
                            }
                        ]
                    },
                    signatures: [new Uint8Array([1, 2, 3, 4, 5])]
                }
            }
        };
    }

    /**
     * 创建模拟的 DLMM 新池创建交易
     */
    private createMockDLMMNewPoolTransaction(): ConfirmedTransaction {
        return {
            slot: *********,
            transaction: {
                meta: {
                    err: null,
                    innerInstructions: [
                        {
                            index: 0,
                            instructions: [
                                {
                                    programIdIndex: 1,
                                    accounts: [0],
                                    data: new Uint8Array([/* 模拟 LbPairCreate 事件数据 */])
                                }
                            ]
                        }
                    ],
                    postTokenBalances: [],
                    preTokenBalances: []
                },
                transaction: {
                    message: {
                        accountKeys: [
                            'new_dlmm_pool_address',
                            'LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo',
                        ],
                        instructions: [
                            {
                                programIdIndex: 1,
                                accounts: [0],
                                data: new Uint8Array([])
                            }
                        ]
                    },
                    signatures: [new Uint8Array([6, 7, 8, 9, 10])]
                }
            }
        };
    }

    /**
     * 创建模拟的 DAMM V1 Swap 交易
     */
    private createMockDAMMV1SwapTransaction(): ConfirmedTransaction {
        return {
            slot: *********,
            transaction: {
                meta: {
                    err: null,
                    innerInstructions: [
                        {
                            index: 0,
                            instructions: [
                                {
                                    programIdIndex: 1,
                                    accounts: [0],
                                    data: new Uint8Array([/* 模拟 DAMM V1 Swap 事件数据 */])
                                }
                            ]
                        }
                    ],
                    postTokenBalances: [],
                    preTokenBalances: []
                },
                transaction: {
                    message: {
                        accountKeys: [
                            'damm_pool_address_123',
                            'Eo7WjKq67rjJQSZxS6z3YkapzY3eMj6Xy8X5EQVn5UaB',
                        ],
                        instructions: [
                            {
                                programIdIndex: 1,
                                accounts: [0],
                                data: new Uint8Array([])
                            }
                        ]
                    },
                    signatures: [new Uint8Array([11, 12, 13, 14, 15])]
                }
            }
        };
    }

    /**
     * 运行所有测试
     */
    async runAllTests(): Promise<void> {
        console.log('🚀 开始优化后的 Meteora 解析器测试');
        console.log('=====================================');

        try {
            await this.testDLMMSwapOptimization();
            await this.testDLMMNewPoolOptimization();
            await this.testDataQualityStats();
            
            console.log('\n🎉 所有测试完成！');
            
        } catch (error) {
            console.error('\n💥 测试过程中出现错误:', error);
        }
    }
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
    const test = new OptimizedMeteoraParserTest();
    test.runAllTests().catch(console.error);
}

export { OptimizedMeteoraParserTest };

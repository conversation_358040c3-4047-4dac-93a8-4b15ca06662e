import { ConfirmedTransaction, SolanaDexTrade, SolanaDexNewPool } from '../../types';
import { BN, Idl } from '@coral-xyz/anchor';
import { idlConfigManager } from '../idl/idlConfigManager';
import * as anchor from '@coral-xyz/anchor';
import bs58 from 'bs58';
import {AnchorProvider, Program, Wallet} from "@coral-xyz/anchor";
import {PublicKey, Connection, Keypair} from "@solana/web3.js";

/**
 * 简化的 Meteora 事件解析器
 * 直接从交易日志中解析 Anchor 事件，而不是复杂的指令解析
 */
export class MeteoraEventParser {
    private idlCache = new Map<string, Idl>();
    private programCache = new Map<string, Program>();
    private provider: AnchorProvider;

    private supportedPrograms: Set<string>;

    constructor(supportedPrograms: string[]) {
        this.supportedPrograms = new Set(supportedPrograms);
        
        // 创建一个简单的provider用于解码（不需要真实连接）
        const dummyConnection = new Connection('https://api.mainnet-beta.solana.com');
        const dummyWallet = new Wallet(Keypair.generate());
        this.provider = new AnchorProvider(dummyConnection, dummyWallet, {});
        
        console.log(`[简化事件解析器] 初始化完成，支持 ${supportedPrograms.length} 个程序`);
    }

    /**
     * 解析交易中的事件 - 简化版本
     */
    async parseTransaction(transaction: ConfirmedTransaction): Promise<(SolanaDexTrade | SolanaDexNewPool)[]> {

        const results: (SolanaDexTrade | SolanaDexNewPool)[] = [];
        if (transaction.transaction.meta.err != undefined) {
            return results
        }
        // 检查必要的数据结构
        if (!transaction.transaction.meta?.innerInstructions) {
            return results;
        }
        // 遍历支持的程序
        for (const programIdString of this.supportedPrograms) {
            try {
                // 获取配置和Program
                const config = idlConfigManager.getConfig(programIdString);
                if (!config) continue;
                
                const program = await this.getProgram(programIdString);
                if (!program) continue;
                
                // 计算事件PDA
                const programId = new PublicKey(programIdString);
                const [eventPDA] = PublicKey.findProgramAddressSync(
                    [Buffer.from("__event_authority")], 
                    programId
                );
                // 找到事件PDA在账户中的索引
                const accountKeys = transaction.transaction?.transaction?.message?.accountKeys || [];
                const indexOfEventPDA = accountKeys.findIndex((key: any) => 
                    new PublicKey(key).equals(eventPDA)
                );
                if (indexOfEventPDA === -1) continue;
                // 查找匹配的事件指令
                const matchingInstructions = transaction.transaction.meta.innerInstructions
                    ?.flatMap((ix:anchor.web3.CompiledInnerInstruction) => ix.instructions)
                    .filter((instruction: any) =>
                        instruction.accounts.length === 1 &&
                        instruction.accounts[0] === indexOfEventPDA
                    );
                if (!matchingInstructions?.length) continue;
                
                // 解析事件
                for (const instruction of matchingInstructions) {
                    try {
                        const ixData = anchor.utils.bytes.bs58.decode(bs58.encode(instruction.data));
                        const eventData = anchor.utils.bytes.base64.encode(ixData.slice(8));
                        
                        // 使用Program.coder.events.decode替代BorshCoder.events.decode
                        const event = program.coder.events.decode(eventData);
                        if (event) {
                            console.log(`✅ 解析到事件: ${event.name}`);
                            console.log(event)
                            const standardEvent = this.createStandardEvent(transaction, event, config);
                            if (standardEvent) {
                                results.push(standardEvent);
                            }
                        }
                    } catch (error) {
                        console.log("error",error)
                    }
                }
            } catch (error) {
                console.error(`解析程序 ${programIdString} 失败:`, error);
            }
        }
        
        return results;
    }



    /**
     * 将 Anchor 事件转换为标准化事件对象
     */
    private createStandardEvent(
        transaction: ConfirmedTransaction,
        anchorEvent: any,
        config: any
    ): SolanaDexTrade | SolanaDexNewPool | null {
        
        const eventName = anchorEvent.name;
        const eventData = anchorEvent.data;

        try {
            // 根据事件名称创建对应的标准化对象
            switch (eventName) {
                case 'Swap':
                    return this.createTradeFromSwapEvent(transaction, eventData, config);

                case 'AddLiquidity':
                    return this.createTradeFromLiquidityEvent(transaction, eventData, config, 'add_liquidity');

                case 'RemoveLiquidity':
                    return this.createTradeFromLiquidityEvent(transaction, eventData, config, 'remove_liquidity');

                case 'InitializeLbPair':
                    return this.createPoolFromInitEvent(transaction, eventData, config);

                default:
                    return null;
            }

        } catch (error) {
            console.error(`💥 创建标准化事件失败:`, error);
            return null;
        }
    }

    /**
     * 从 Swap 事件创建交易对象
     * 数据映射: eventData.lbPair->poolAddress, eventData.from->traderAddress
     * BN转换: 使用Anchor的BN.toString()直接转换大数
     */
    private createTradeFromSwapEvent(
        transaction: ConfirmedTransaction,
        eventData: any,
        config: any
    ): SolanaDexTrade {
        console.log("Swap事件数据:", eventData);

        // 使用Anchor BN直接转换
        const amountIn = new BN(eventData.amountIn).toString();
        const amountOut = new BN(eventData.amountOut).toString();

        // 根据swapForY判断交易方向和交易类型
        const isSwapForY = eventData.swapForY;

        // 根据交易方向分配代币数量
        const token0Amount = isSwapForY ? amountIn : amountOut;
        const token1Amount = isSwapForY ? amountOut : amountIn;

        // 计算token0单价 (token1Amount / token0Amount)
        let token0UnitPrice = '0';
        try {
            const token0Decimal = new BN(token0Amount);
            const token1Decimal = new BN(token1Amount);
            if (!token0Decimal.isZero()) {
                // 使用高精度计算，保留18位小数
                const price = token1Decimal.mul(new BN(10).pow(new BN(18))).div(token0Decimal);
                token0UnitPrice = price.toString();
                // 转换为小数形式
                const priceStr = price.toString().padStart(19, '0');
                const integerPart = priceStr.slice(0, -18) || '0';
                const decimalPart = priceStr.slice(-18).replace(/0+$/, '');
                token0UnitPrice = decimalPart ? `${integerPart}.${decimalPart}` : integerPart;
            }
        } catch (error) {
            console.warn('计算token0UnitPrice失败:', error);
        }

        // 判断交易类型：如果买入的是稳定币或原生币，则为sell，否则为buy
        // 这里需要根据实际的代币地址来判断，暂时使用简化逻辑
        const txType: 'buy' | 'sell' = isSwapForY ? 'buy' : 'sell';

        // 格式化指令索引 (对应Go端的instruction.Index)
        const instructionIndex = this.formatInstructionIndex(transaction);

        return {
            dexName: config.name,
            poolAddress: eventData.lbPair.toString(),
            txHash: bs58.encode(transaction.transaction.transaction.signatures),
            txType,
            slot: transaction.slot || 0,
            instructionIndex,
            timestamp: Math.floor(Date.now() / 1000),
            traderAddress: eventData.from.toString(),
            token0Address: 'unknown', // 需要从池配置获取
            token1Address: 'unknown', // 需要从池配置获取
            token0Amount,
            token1Amount,
            token0Reserve: '0', // 需要从交易中解析
            token1Reserve: '0', // 需要从交易中解析
            dexSource: 'Event-' + config.name,
        };
    }

    /**
     * 从流动性事件创建交易对象
     */
    private createTradeFromLiquidityEvent(
        transaction: ConfirmedTransaction,
        eventData: any,
        config: any,
        txType: 'add_liquidity' | 'remove_liquidity'
    ): SolanaDexTrade {
        return {
            dexName: config.name,
            poolAddress: eventData.lbPair || eventData.pool || 'unknown',
            txHash: bs58.encode(transaction.transaction.transaction.signatures),
            txType,
            slot: transaction.slot || 0,
            instructionIndex: '0',
            timestamp: Math.floor(Date.now() / 1000),
            traderAddress: eventData.from || eventData.user || 'unknown',
            token0Address: 'unknown', // 需要从池信息获取
            token1Address: 'unknown',
            token0Amount: eventData.amounts?.[0]?.toString() || '0',
            token1Amount: eventData.amounts?.[1]?.toString() || '0',
            token0Reserve: '0',
            token1Reserve: '0',
            dexSource: 'Event-' + config.name,
        };
    }

    /**
     * 从初始化事件创建新池对象
     */
    private createPoolFromInitEvent(
        transaction: ConfirmedTransaction,
        eventData: any,
        config: any
    ): SolanaDexNewPool {
        return {
            dexName: config.name,
            pairAddress: eventData.lbPair || eventData.pool || 'unknown',
            token0Address: eventData.tokenX || eventData.tokenA || 'unknown',
            token1Address: eventData.tokenY || eventData.tokenB || 'unknown',
            creationTxHash: bs58.encode(transaction.transaction.transaction.signatures),
            blockNumber: transaction.slot || 0,
            timestamp: Math.floor(Date.now() / 1000),
        };
    }

    // 复用原有的 IDL 和 Coder 管理方法
    private async getIDL(config: any): Promise<Idl | null> {
        if (this.idlCache.has(config.programId)) {
            return this.idlCache.get(config.programId)!;
        }

        try {
            // Dynamically import the TypeScript module directly
            const idlModule = await import(config.path);
            
            // Access the exported IDL constant
            const idl = idlModule.IDL as Idl;
            
            if (!idl) {
                console.error(`IDL not found in module: ${config.path}`);
                return null;
            }
            
            this.idlCache.set(config.programId, idl);
            return idl;
        } catch (error) {
            console.error(`Error loading IDL from ${config.path}:`, error);
            return null;
        }
    }

    private async getProgram(programIdString: string): Promise<Program | null> {
        if (this.programCache.has(programIdString)) {
            return this.programCache.get(programIdString)!;
        }

        const config = idlConfigManager.getConfig(programIdString);
        if (!config) {
            console.error(`未找到 programId=${programIdString} 的配置`);
            return null;
        }

        const idl = await this.getIDL(config);
        if (!idl) {
            console.error(`IDL 加载失败 for programId=${programIdString}`);
            return null;
        }

        const program = new Program<Idl>(idl,new PublicKey(programIdString).toBase58() ,this.provider);
        this.programCache.set(programIdString, program);
        return program;
    }


}
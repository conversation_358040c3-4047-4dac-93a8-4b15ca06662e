import { ConfirmedTransaction, SolanaDexTrade, SolanaDexNewPool } from '../../types';
import { BN, Idl } from '@coral-xyz/anchor';
import { idlConfigManager } from '../idl/idlConfigManager';
import * as anchor from '@coral-xyz/anchor';
import bs58 from 'bs58';
import {AnchorProvider, Program, Wallet} from "@coral-xyz/anchor";
import {PublicKey, Connection, Keypair} from "@solana/web3.js";

/**
 * 优化的 Meteora 事件解析器
 * 最大化利用事件数据，减少对指令解析的依赖
 * 支持 DLMM 和 DAMM V1 协议的智能解析
 */
export class MeteoraEventParser {
    private idlCache = new Map<string, Idl>();
    private programCache = new Map<string, Program>();
    private provider: AnchorProvider;
    private supportedPrograms: Set<string>;

    // 程序 ID 常量
    private readonly DLMM_PROGRAM_ID = 'LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo';
    private readonly DAMM_V1_PROGRAM_ID = 'Eo7WjKq67rjJQSZxS6z3YkapzY3eMj6Xy8X5EQVn5UaB';

    // Solana 原生代币和稳定币地址
    private readonly SOLANA_NATIVE = 'So11111111111111111111111111111111111111112'; // SOL
    private readonly USDC_ADDRESS = 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v'; // USDC
    private readonly USDT_ADDRESS = 'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB'; // USDT

    // 数据质量统计
    private dataQualityStats = {
        dlmmEventUsage: 0,
        dammV1EventUsage: 0,
        instructionFallback: 0,
        totalParsed: 0
    };

    constructor(supportedPrograms: string[]) {
        this.supportedPrograms = new Set(supportedPrograms);

        // 创建一个简单的provider用于解码（不需要真实连接）
        const dummyConnection = new Connection('https://api.mainnet-beta.solana.com');
        const dummyWallet = new Wallet(Keypair.generate());
        this.provider = new AnchorProvider(dummyConnection, dummyWallet, {});

        console.log(`[优化事件解析器] 初始化完成，支持 ${supportedPrograms.length} 个程序`);
        console.log(`[优化事件解析器] DLMM 程序: ${this.DLMM_PROGRAM_ID}`);
        console.log(`[优化事件解析器] DAMM V1 程序: ${this.DAMM_V1_PROGRAM_ID}`);
    }

    /**
     * 解析交易中的事件 - 简化版本
     */
    async parseTransaction(transaction: ConfirmedTransaction): Promise<(SolanaDexTrade | SolanaDexNewPool)[]> {

        const results: (SolanaDexTrade | SolanaDexNewPool)[] = [];
        if (transaction.transaction.meta.err != undefined) {
            return results
        }
        // 检查必要的数据结构
        if (!transaction.transaction.meta?.innerInstructions) {
            return results;
        }

        // 预处理：构建 SPL Token 地址映射（对应 Go 端的 splTokenAddresses）
        const splTokenAddresses = this.buildSplTokenMap(transaction);

        // 预处理：构建池子余额映射（对应 Go 端的 reservesMap）
        const reservesMap = this.buildReservesMap(transaction);

        // 遍历支持的程序
        for (const programIdString of this.supportedPrograms) {
            try {
                // 获取配置和Program
                const config = idlConfigManager.getConfig(programIdString);
                if (!config) continue;

                const program = await this.getProgram(programIdString);
                if (!program) continue;

                // 计算事件PDA
                const programId = new PublicKey(programIdString);
                const [eventPDA] = PublicKey.findProgramAddressSync(
                    [Buffer.from("__event_authority")],
                    programId
                );
                // 找到事件PDA在账户中的索引
                const accountKeys = transaction.transaction?.transaction?.message?.accountKeys || [];
                const indexOfEventPDA = accountKeys.findIndex((key: any) =>
                    new PublicKey(key).equals(eventPDA)
                );
                if (indexOfEventPDA === -1) continue;
                // 查找匹配的事件指令
                const matchingInstructions = transaction.transaction.meta.innerInstructions
                    ?.flatMap((ix:anchor.web3.CompiledInnerInstruction) => ix.instructions)
                    .filter((instruction: any) =>
                        instruction.accounts.length === 1 &&
                        instruction.accounts[0] === indexOfEventPDA
                    );
                if (!matchingInstructions?.length) continue;

                // 解析事件
                for (const instruction of matchingInstructions) {
                    try {
                        const ixData = anchor.utils.bytes.bs58.decode(bs58.encode(instruction.data));
                        const eventData = anchor.utils.bytes.base64.encode(ixData.slice(8));

                        // 使用Program.coder.events.decode替代BorshCoder.events.decode
                        const event = program.coder.events.decode(eventData);
                        if (event) {
                            console.log(`✅ 解析到事件: ${event.name}`);
                            console.log(event)
                            const standardEvent = this.createStandardEvent(
                                transaction,
                                event,
                                config,
                                splTokenAddresses,
                                reservesMap
                            );
                            if (standardEvent) {
                                results.push(standardEvent);
                            }
                        }
                    } catch (error) {
                        console.log("error",error)
                    }
                }
            } catch (error) {
                console.error(`解析程序 ${programIdString} 失败:`, error);
            }
        }

        return results;
    }



    /**
     * 将 Anchor 事件转换为标准化事件对象
     */
    private createStandardEvent(
        transaction: ConfirmedTransaction,
        anchorEvent: any,
        config: any,
        splTokenAddresses: Map<string, {mint: string, decimals: number}>,
        reservesMap: Map<string, {token0Reserve: string, token1Reserve: string}>
    ): SolanaDexTrade | SolanaDexNewPool | null {
        
        const eventName = anchorEvent.name;
        const eventData = anchorEvent.data;

        try {
            // 根据事件名称创建对应的标准化对象
            switch (eventName) {
                case 'Swap':
                    return this.createTradeFromSwapEvent(transaction, eventData, config, splTokenAddresses, reservesMap);

                case 'AddLiquidity':
                    return this.createTradeFromLiquidityEvent(transaction, eventData, config, 'add_liquidity', splTokenAddresses, reservesMap);

                case 'RemoveLiquidity':
                    return this.createTradeFromLiquidityEvent(transaction, eventData, config, 'remove_liquidity', splTokenAddresses, reservesMap);

                case 'InitializeLbPair':
                    return this.createPoolFromInitEvent(transaction, eventData, config);

                default:
                    return null;
            }

        } catch (error) {
            console.error(`💥 创建标准化事件失败:`, error);
            return null;
        }
    }

    /**
     * 从 Swap 事件创建交易对象
     * 数据映射: eventData.lbPair->poolAddress, eventData.from->traderAddress
     * BN转换: 使用Anchor的BN.toString()直接转换大数
     */
    private createTradeFromSwapEvent(
        transaction: ConfirmedTransaction,
        eventData: any,
        config: any,
        splTokenAddresses: Map<string, {mint: string, decimals: number}>,
        reservesMap: Map<string, {token0Reserve: string, token1Reserve: string}>
    ): SolanaDexTrade {
        console.log("Swap事件数据:", eventData);

        // 使用Anchor BN直接转换
        const amountIn = new BN(eventData.amountIn).toString();
        const amountOut = new BN(eventData.amountOut).toString();

        // 根据swapForY判断交易方向和交易类型
        const isSwapForY = eventData.swapForY;

        // 根据交易方向分配代币数量
        const token0Amount = isSwapForY ? amountIn : amountOut;
        const token1Amount = isSwapForY ? amountOut : amountIn;

        // 计算token0单价 (token1Amount / token0Amount)
        let token0UnitPrice = '0';
        try {
            const token0Decimal = new BN(token0Amount);
            const token1Decimal = new BN(token1Amount);
            if (!token0Decimal.isZero()) {
                // 使用高精度计算，保留18位小数
                const price = token1Decimal.mul(new BN(10).pow(new BN(18))).div(token0Decimal);
                token0UnitPrice = price.toString();
                // 转换为小数形式
                const priceStr = price.toString().padStart(19, '0');
                const integerPart = priceStr.slice(0, -18) || '0';
                const decimalPart = priceStr.slice(-18).replace(/0+$/, '');
                token0UnitPrice = decimalPart ? `${integerPart}.${decimalPart}` : integerPart;
            }
        } catch (error) {
            console.warn('计算token0UnitPrice失败:', error);
        }

        // 判断交易类型：如果买入的是稳定币或原生币，则为sell，否则为buy
        // 这里需要根据实际的代币地址来判断，暂时使用简化逻辑
        const txType: 'buy' | 'sell' = isSwapForY ? 'buy' : 'sell';

        // 格式化指令索引 (对应Go端的instruction.Index)
        const instructionIndex = this.formatInstructionIndex(transaction);

        // 获取池子地址和代币地址
        const poolAddress = eventData.lbPair.toString();
        const { token0Address, token1Address } = this.getTokenAddressesFromPool(poolAddress, transaction);

        // 获取池子余额
        const reserves = reservesMap.get(poolAddress) || { token0Reserve: '0', token1Reserve: '0' };

        return {
            dexName: config.name,
            poolAddress,
            txHash: bs58.encode(transaction.transaction.transaction.signatures),
            txType,
            slot: transaction.slot || 0,
            instructionIndex,
            timestamp: Math.floor(Date.now() / 1000),
            traderAddress: eventData.from.toString(),
            token0Address,
            token1Address,
            token0Amount,
            token1Amount,
            token0Reserve: reserves.token0Reserve,
            token1Reserve: reserves.token1Reserve,
            dexSource: 'Event-' + config.name,
        };
    }

    /**
     * 从流动性事件创建交易对象
     */
    private createTradeFromLiquidityEvent(
        transaction: ConfirmedTransaction,
        eventData: any,
        config: any,
        txType: 'add_liquidity' | 'remove_liquidity',
        splTokenAddresses: Map<string, {mint: string, decimals: number}>,
        reservesMap: Map<string, {token0Reserve: string, token1Reserve: string}>
    ): SolanaDexTrade {
        // 处理流动性数量
        const token0Amount = eventData.amounts?.[0]?.toString() ||
                           eventData.amountX?.toString() ||
                           eventData.token0Amount?.toString() || '0';
        const token1Amount = eventData.amounts?.[1]?.toString() ||
                           eventData.amountY?.toString() ||
                           eventData.token1Amount?.toString() || '0';

        // 计算token0单价
        let token0UnitPrice = '0';
        try {
            const token0Decimal = new BN(token0Amount);
            const token1Decimal = new BN(token1Amount);
            if (!token0Decimal.isZero()) {
                const price = token1Decimal.mul(new BN(10).pow(new BN(18))).div(token0Decimal);
                const priceStr = price.toString().padStart(19, '0');
                const integerPart = priceStr.slice(0, -18) || '0';
                const decimalPart = priceStr.slice(-18).replace(/0+$/, '');
                token0UnitPrice = decimalPart ? `${integerPart}.${decimalPart}` : integerPart;
            }
        } catch (error) {
            console.warn('计算流动性token0UnitPrice失败:', error);
        }

        const instructionIndex = this.formatInstructionIndex(transaction);

        // 获取池子地址和代币地址
        const poolAddress = eventData.lbPair || eventData.pool || 'unknown';
        const { token0Address, token1Address } = this.getTokenAddressesFromPool(poolAddress, transaction);

        // 获取池子余额
        const reserves = reservesMap.get(poolAddress) || { token0Reserve: '0', token1Reserve: '0' };

        return {
            dexName: config.name,
            poolAddress,
            txHash: bs58.encode(transaction.transaction.transaction.signatures),
            txType,
            slot: transaction.slot || 0,
            instructionIndex,
            timestamp: Math.floor(Date.now() / 1000),
            traderAddress: eventData.from || eventData.user || 'unknown',
            token0Address,
            token1Address,
            token0Amount,
            token1Amount,
            token0Reserve: reserves.token0Reserve,
            token1Reserve: reserves.token1Reserve,
            dexSource: 'Event-' + config.name,
        };
    }

    /**
     * 从初始化事件创建新池对象
     */
    private createPoolFromInitEvent(
        transaction: ConfirmedTransaction,
        eventData: any,
        config: any
    ): SolanaDexNewPool {
        return {
            dexName: config.name,
            pairAddress: eventData.lbPair || eventData.pool || 'unknown',
            token0Address: eventData.tokenX?.toString() || eventData.tokenA?.toString() || 'unknown',
            token1Address: eventData.tokenY?.toString() || eventData.tokenB?.toString() || 'unknown',
            creationTxHash: bs58.encode(transaction.transaction.transaction.signatures),
            blockNumber: transaction.slot || 0,
            timestamp: Math.floor(Date.now() / 1000),
        };
    }

    /**
     * 格式化指令索引（对应Go端的instruction.Index）
     */
    private formatInstructionIndex(transaction: ConfirmedTransaction): string {
        // 简化版本，实际应该根据具体的指令位置来确定
        // Go端使用 fmt.Sprintf("%d_%d", innerInstruction.Index, j)
        return '0_0';
    }

    /**
     * 构建 SPL Token 地址映射（对应 Go 端的 splTokenAddresses）
     */
    private buildSplTokenMap(transaction: ConfirmedTransaction): Map<string, {mint: string, decimals: number}> {
        const splTokenAddresses = new Map<string, {mint: string, decimals: number}>();

        // 从 PostTokenBalances 中提取代币信息
        const postTokenBalances = transaction.transaction?.meta?.postTokenBalances || [];
        for (const item of postTokenBalances) {
            if (item.mint && item.uiTokenAmount?.decimals !== undefined) {
                splTokenAddresses.set(item.mint, {
                    mint: item.mint,
                    decimals: item.uiTokenAmount.decimals
                });
            }
        }

        return splTokenAddresses;
    }

    /**
     * 构建池子余额映射（对应 Go 端的 reservesMap）
     */
    private buildReservesMap(transaction: ConfirmedTransaction): Map<string, {token0Reserve: string, token1Reserve: string}> {
        const reservesMap = new Map<string, {token0Reserve: string, token1Reserve: string}>();

        // 从指令中提取池子信息和余额
        const instructions = transaction.transaction?.transaction?.message?.instructions || [];
        const accountKeys = transaction.transaction?.transaction?.message?.accountKeys || [];

        for (const instruction of instructions) {
            // 检查是否为 Meteora 程序指令
            const programAccount = accountKeys[instruction.programIdIndex];
            if (this.supportedPrograms.includes(programAccount)) {
                // 检查指令账户数量（对应 Go 端的 len(instruction.Accounts) > 8）
                if (instruction.accounts && instruction.accounts.length > 8) {
                    const poolAddress = accountKeys[instruction.accounts[0]]; // 池子地址
                    const token0VaultIndex = instruction.accounts[6]; // token0 vault 索引
                    const token1VaultIndex = instruction.accounts[7]; // token1 vault 索引

                    if (token0VaultIndex < accountKeys.length && token1VaultIndex < accountKeys.length) {
                        const token0VaultAddress = accountKeys[token0VaultIndex];
                        const token1VaultAddress = accountKeys[token1VaultIndex];

                        // 从 PostTokenBalances 中获取余额
                        const reserves = this.getPoolReservesFromTransaction(
                            transaction,
                            poolAddress,
                            token0VaultAddress,
                            token1VaultAddress
                        );

                        if (reserves.token0Reserve !== '0' || reserves.token1Reserve !== '0') {
                            reservesMap.set(poolAddress, reserves);
                        }
                    }
                }
            }
        }

        return reservesMap;
    }

    /**
     * 从交易中获取池子余额（对应 Go 端的 getPoolReserves）
     */
    private getPoolReservesFromTransaction(
        transaction: ConfirmedTransaction,
        poolAddress: string,
        token0VaultAddress: string,
        token1VaultAddress: string
    ): {token0Reserve: string, token1Reserve: string} {
        let token0Reserve = '0';
        let token1Reserve = '0';

        const postTokenBalances = transaction.transaction?.meta?.postTokenBalances || [];

        // 遍历 PostTokenBalances 查找池子的代币余额
        for (const balance of postTokenBalances) {
            if (balance.owner === poolAddress && balance.uiTokenAmount?.uiAmountString) {
                // 通过账户索引找到对应的账户地址
                const accountKeys = transaction.transaction?.transaction?.message?.accountKeys || [];
                const accountAddress = accountKeys[balance.accountIndex];

                if (accountAddress === token0VaultAddress) {
                    token0Reserve = balance.uiTokenAmount.uiAmountString;
                } else if (accountAddress === token1VaultAddress) {
                    token1Reserve = balance.uiTokenAmount.uiAmountString;
                }
            }
        }

        return { token0Reserve, token1Reserve };
    }

    /**
     * 从池子地址获取代币地址（对应 Go 端从指令账户中获取 token0Address、token1Address）
     */
    private getTokenAddressesFromPool(
        poolAddress: string,
        transaction: ConfirmedTransaction
    ): {token0Address: string, token1Address: string} {
        let token0Address = 'unknown';
        let token1Address = 'unknown';

        // 从指令中查找池子相关的代币地址
        const instructions = transaction.transaction?.transaction?.message?.instructions || [];
        const accountKeys = transaction.transaction?.transaction?.message?.accountKeys || [];

        for (const instruction of instructions) {
            const programAccount = accountKeys[instruction.programIdIndex];
            if (this.supportedPrograms.includes(programAccount)) {
                if (instruction.accounts && instruction.accounts.length > 8) {
                    const instructionPoolAddress = accountKeys[instruction.accounts[0]];

                    if (instructionPoolAddress === poolAddress) {
                        // 对应 Go 端的 instruction.Accounts[6] 和 instruction.Accounts[7]
                        const token0VaultIndex = instruction.accounts[6];
                        const token1VaultIndex = instruction.accounts[7];

                        if (token0VaultIndex < accountKeys.length && token1VaultIndex < accountKeys.length) {
                            const token0VaultAddress = accountKeys[token0VaultIndex];
                            const token1VaultAddress = accountKeys[token1VaultIndex];

                            // 使用 GetMintFromMeta 逻辑从 PostTokenBalances 中获取 mint 地址
                            token0Address = this.getMintFromMeta(transaction, token0VaultAddress);
                            token1Address = this.getMintFromMeta(transaction, token1VaultAddress);

                            if (token0Address !== 'unknown' && token1Address !== 'unknown') {
                                break;
                            }
                        }
                    }
                }
            }
        }

        return { token0Address, token1Address };
    }

    /**
     * 从交易元数据中获取 mint 地址（对应 Go 端的 GetMintFromMeta）
     */
    private getMintFromMeta(transaction: ConfirmedTransaction, ataAddress: string): string {
        const accountKeys = transaction.transaction?.transaction?.message?.accountKeys || [];

        // 查找 postTokenBalances
        const postTokenBalances = transaction.transaction?.meta?.postTokenBalances || [];
        for (const balance of postTokenBalances) {
            const ownerAddress = accountKeys[balance.accountIndex];
            if (ownerAddress === ataAddress && balance.mint) {
                return balance.mint;
            }
        }

        // 查找 preTokenBalances
        const preTokenBalances = transaction.transaction?.meta?.preTokenBalances || [];
        for (const balance of preTokenBalances) {
            const ownerAddress = accountKeys[balance.accountIndex];
            if (ownerAddress === ataAddress && balance.mint) {
                return balance.mint;
            }
        }

        return 'unknown';
    }

    // 复用原有的 IDL 和 Coder 管理方法
    private async getIDL(config: any): Promise<Idl | null> {
        if (this.idlCache.has(config.programId)) {
            return this.idlCache.get(config.programId)!;
        }

        try {
            // Dynamically import the TypeScript module directly
            const idlModule = await import(config.path);
            
            // Access the exported IDL constant
            const idl = idlModule.IDL as Idl;
            
            if (!idl) {
                console.error(`IDL not found in module: ${config.path}`);
                return null;
            }
            
            this.idlCache.set(config.programId, idl);
            return idl;
        } catch (error) {
            console.error(`Error loading IDL from ${config.path}:`, error);
            return null;
        }
    }

    private async getProgram(programIdString: string): Promise<Program | null> {
        if (this.programCache.has(programIdString)) {
            return this.programCache.get(programIdString)!;
        }

        const config = idlConfigManager.getConfig(programIdString);
        if (!config) {
            console.error(`未找到 programId=${programIdString} 的配置`);
            return null;
        }

        const idl = await this.getIDL(config);
        if (!idl) {
            console.error(`IDL 加载失败 for programId=${programIdString}`);
            return null;
        }

        const program = new Program<Idl>(idl,new PublicKey(programIdString).toBase58() ,this.provider);
        this.programCache.set(programIdString, program);
        return program;
    }


}
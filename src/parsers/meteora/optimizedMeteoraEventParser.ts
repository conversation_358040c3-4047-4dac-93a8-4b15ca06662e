import * as anchor from '@coral-xyz/anchor';
import { PublicKey } from '@solana/web3.js';
import { BN } from '@coral-xyz/anchor';
import bs58 from 'bs58';
import { ConfirmedTransaction, SolanaDexTrade, SolanaDexNewPool } from '../../types';
import { idlConfigManager } from '../idl/idlConfigManager';

/**
 * 优化的 Meteora 事件解析器
 * 最大化利用 IDL 事件中的数据，减少对指令解析的依赖
 */
export class OptimizedMeteoraEventParser {
    private supportedPrograms: string[];
    private programCache = new Map<string, anchor.Program>();

    constructor(supportedPrograms: string[]) {
        this.supportedPrograms = supportedPrograms;
    }

    /**
     * 解析交易中的事件 - 优化版本，最大化利用事件数据
     */
    async parseTransaction(transaction: ConfirmedTransaction): Promise<(SolanaDexTrade | SolanaDexNewPool)[]> {
        const results: (SolanaDexTrade | SolanaDexNewPool)[] = [];
        
        if (transaction.transaction.meta.err != undefined) {
            return results;
        }

        if (!transaction.transaction.meta?.innerInstructions) {
            return results;
        }

        // 预处理：构建交易上下文数据
        const transactionContext = this.buildTransactionContext(transaction);

        // 遍历支持的程序
        for (const programIdString of this.supportedPrograms) {
            try {
                const config = idlConfigManager.getConfig(programIdString);
                if (!config) continue;
                
                const program = await this.getProgram(programIdString);
                if (!program) continue;
                
                // 查找事件指令
                const eventInstructions = this.findEventInstructions(transaction, programIdString);
                
                // 解析每个事件
                for (const { instruction, instructionIndex } of eventInstructions) {
                    try {
                        const ixData = anchor.utils.bytes.bs58.decode(bs58.encode(instruction.data));
                        const eventData = anchor.utils.bytes.base64.encode(ixData.slice(8));
                        
                        const event = program.coder.events.decode(eventData);
                        if (event) {
                            console.log(`✅ 解析到事件: ${event.name}`);
                            
                            const standardEvent = this.createOptimizedEvent(
                                transaction, 
                                event, 
                                config, 
                                transactionContext,
                                instructionIndex
                            );
                            
                            if (standardEvent) {
                                results.push(standardEvent);
                            }
                        }
                    } catch (error) {
                        console.log("事件解析错误:", error);
                    }
                }
            } catch (error) {
                console.error(`解析程序 ${programIdString} 失败:`, error);
            }
        }
        
        return results;
    }

    /**
     * 构建交易上下文数据
     */
    private buildTransactionContext(transaction: ConfirmedTransaction) {
        return {
            // SPL Token 映射
            splTokenMap: this.buildSplTokenMap(transaction),
            // 池子余额映射
            reservesMap: this.buildReservesMap(transaction),
            // 指令映射
            instructionMap: this.buildInstructionMap(transaction),
            // 账户键映射
            accountKeys: transaction.transaction?.transaction?.message?.accountKeys || []
        };
    }

    /**
     * 创建优化的事件对象 - 最大化利用事件数据
     */
    private createOptimizedEvent(
        transaction: ConfirmedTransaction,
        anchorEvent: any,
        config: any,
        context: any,
        instructionIndex: number
    ): SolanaDexTrade | SolanaDexNewPool | null {
        const eventName = anchorEvent.name;
        const eventData = anchorEvent.data;

        console.log(`处理事件: ${eventName}`, eventData);

        switch (eventName) {
            case 'Swap':
                return this.createOptimizedSwapTrade(transaction, eventData, config, context, instructionIndex);

            case 'AddLiquidity':
                return this.createOptimizedLiquidityTrade(transaction, eventData, config, context, instructionIndex, 'add_liquidity');

            case 'RemoveLiquidity':
                return this.createOptimizedLiquidityTrade(transaction, eventData, config, context, instructionIndex, 'remove_liquidity');

            case 'LbPairCreate':
                return this.createOptimizedNewPool(transaction, eventData, config, context);

            case 'BootstrapLiquidity':
                // DAMM V1 的池子创建事件
                return this.createOptimizedBootstrapPool(transaction, eventData, config, context);

            default:
                console.log(`未处理的事件类型: ${eventName}`);
                return null;
        }
    }

    /**
     * 创建优化的 Swap 交易 - 最大化利用事件数据
     */
    private createOptimizedSwapTrade(
        transaction: ConfirmedTransaction,
        eventData: any,
        config: any,
        context: any,
        instructionIndex: number
    ): SolanaDexTrade {
        // DLMM 事件包含完整信息
        if (eventData.lbPair && eventData.from) {
            return this.createDLMMSwapTrade(transaction, eventData, config, context, instructionIndex);
        }
        
        // DAMM V1 事件需要补充信息
        return this.createDAMMV1SwapTrade(transaction, eventData, config, context, instructionIndex);
    }

    /**
     * 创建 DLMM Swap 交易 - 事件数据完整
     */
    private createDLMMSwapTrade(
        transaction: ConfirmedTransaction,
        eventData: any,
        config: any,
        context: any,
        instructionIndex: number
    ): SolanaDexTrade {
        const poolAddress = eventData.lbPair.toString();
        const traderAddress = eventData.from.toString();
        
        // 从事件直接获取交易金额
        const amountIn = new BN(eventData.amountIn).toString();
        const amountOut = new BN(eventData.amountOut).toString();
        
        // 从事件获取交易方向
        const swapForY = eventData.swapForY;
        const txType: 'buy' | 'sell' = this.determineTxType(swapForY, context, poolAddress);
        
        // 根据交易方向分配代币数量
        const token0Amount = swapForY ? amountIn : amountOut;
        const token1Amount = swapForY ? amountOut : amountIn;
        
        // 获取代币地址和余额
        const { token0Address, token1Address } = this.getTokenAddressesFromContext(poolAddress, context);
        const reserves = context.reservesMap.get(poolAddress) || { token0Reserve: '0', token1Reserve: '0' };
        
        return {
            dexName: config.name,
            poolAddress,
            txHash: bs58.encode(transaction.transaction.transaction.signatures),
            txType,
            slot: transaction.slot || 0,
            instructionIndex: `${instructionIndex}_0`,
            timestamp: Math.floor(Date.now() / 1000),
            traderAddress,
            token0Address,
            token1Address,
            token0Amount,
            token1Amount,
            token0Reserve: reserves.token0Reserve,
            token1Reserve: reserves.token1Reserve,
            dexSource: 'Event-' + config.name,
        };
    }

    /**
     * 创建 DAMM V1 Swap 交易 - 需要补充指令数据
     */
    private createDAMMV1SwapTrade(
        transaction: ConfirmedTransaction,
        eventData: any,
        config: any,
        context: any,
        instructionIndex: number
    ): SolanaDexTrade {
        // 从事件获取交易金额
        const inAmount = new BN(eventData.inAmount).toString();
        const outAmount = new BN(eventData.outAmount).toString();
        
        // 从指令获取池子和用户信息
        const instructionData = this.getInstructionData(instructionIndex, context);
        const poolAddress = instructionData.poolAddress || 'unknown';
        const traderAddress = instructionData.userAddress || 'unknown';
        
        // 需要额外逻辑判断交易方向
        const txType = this.determineDAMMV1TxType(instructionData, inAmount, outAmount);
        
        // 获取代币地址和余额
        const { token0Address, token1Address } = this.getTokenAddressesFromContext(poolAddress, context);
        const reserves = context.reservesMap.get(poolAddress) || { token0Reserve: '0', token1Reserve: '0' };
        
        return {
            dexName: config.name,
            poolAddress,
            txHash: bs58.encode(transaction.transaction.transaction.signatures),
            txType,
            slot: transaction.slot || 0,
            instructionIndex: `${instructionIndex}_0`,
            timestamp: Math.floor(Date.now() / 1000),
            traderAddress,
            token0Address,
            token1Address,
            token0Amount: inAmount,
            token1Amount: outAmount,
            token0Reserve: reserves.token0Reserve,
            token1Reserve: reserves.token1Reserve,
            dexSource: 'Event-' + config.name,
        };
    }

    /**
     * 创建优化的流动性交易
     */
    private createOptimizedLiquidityTrade(
        transaction: ConfirmedTransaction,
        eventData: any,
        config: any,
        context: any,
        instructionIndex: number,
        txType: 'add_liquidity' | 'remove_liquidity'
    ): SolanaDexTrade {
        // DLMM 事件包含完整信息
        if (eventData.lbPair && eventData.from && eventData.amounts) {
            const poolAddress = eventData.lbPair.toString();
            const traderAddress = eventData.from.toString();
            
            // 从事件数组获取代币数量
            const token0Amount = new BN(eventData.amounts[0]).toString();
            const token1Amount = new BN(eventData.amounts[1]).toString();
            
            const { token0Address, token1Address } = this.getTokenAddressesFromContext(poolAddress, context);
            const reserves = context.reservesMap.get(poolAddress) || { token0Reserve: '0', token1Reserve: '0' };
            
            return {
                dexName: config.name,
                poolAddress,
                txHash: bs58.encode(transaction.transaction.transaction.signatures),
                txType,
                slot: transaction.slot || 0,
                instructionIndex: `${instructionIndex}_0`,
                timestamp: Math.floor(Date.now() / 1000),
                traderAddress,
                token0Address,
                token1Address,
                token0Amount,
                token1Amount,
                token0Reserve: reserves.token0Reserve,
                token1Reserve: reserves.token1Reserve,
                dexSource: 'Event-' + config.name,
            };
        }
        
        // DAMM V1 流动性事件处理
        return this.createDAMMV1LiquidityTrade(transaction, eventData, config, context, instructionIndex, txType);
    }

    /**
     * 创建优化的新池对象 - DLMM 包含代币地址
     */
    private createOptimizedNewPool(
        transaction: ConfirmedTransaction,
        eventData: any,
        config: any,
        context: any
    ): SolanaDexNewPool {
        return {
            dexName: config.name,
            pairAddress: eventData.lbPair.toString(),
            token0Address: eventData.tokenX.toString(),  // ✅ 直接从事件获取
            token1Address: eventData.tokenY.toString(),  // ✅ 直接从事件获取
            creationTxHash: bs58.encode(transaction.transaction.transaction.signatures),
            blockNumber: transaction.slot || 0,
            timestamp: Math.floor(Date.now() / 1000),
        };
    }

    // ... 其他辅助方法
    private async getProgram(programIdString: string): Promise<anchor.Program | null> {
        if (this.programCache.has(programIdString)) {
            return this.programCache.get(programIdString)!;
        }

        try {
            const config = idlConfigManager.getConfig(programIdString);
            if (!config) return null;

            const programId = new PublicKey(programIdString);
            const program = new anchor.Program(config.idl, programId);
            this.programCache.set(programIdString, program);
            return program;
        } catch (error) {
            console.error(`创建程序实例失败 ${programIdString}:`, error);
            return null;
        }
    }

    private findEventInstructions(transaction: ConfirmedTransaction, programIdString: string) {
        // 实现查找事件指令的逻辑
        return [];
    }

    private buildSplTokenMap(transaction: ConfirmedTransaction) {
        // 实现 SPL Token 映射构建
        return new Map();
    }

    private buildReservesMap(transaction: ConfirmedTransaction) {
        // 实现池子余额映射构建
        return new Map();
    }

    private buildInstructionMap(transaction: ConfirmedTransaction) {
        // 实现指令映射构建
        return new Map();
    }

    private determineTxType(swapForY: boolean, context: any, poolAddress: string): 'buy' | 'sell' {
        // 实现交易类型判断逻辑
        return swapForY ? 'buy' : 'sell';
    }

    private getTokenAddressesFromContext(poolAddress: string, context: any) {
        // 实现从上下文获取代币地址
        return { token0Address: 'unknown', token1Address: 'unknown' };
    }

    private getInstructionData(instructionIndex: number, context: any) {
        // 实现从指令获取数据
        return { poolAddress: 'unknown', userAddress: 'unknown' };
    }

    private determineDAMMV1TxType(instructionData: any, inAmount: string, outAmount: string): 'buy' | 'sell' {
        // 实现 DAMM V1 交易类型判断
        return 'buy';
    }

    private createDAMMV1LiquidityTrade(
        transaction: ConfirmedTransaction,
        eventData: any,
        config: any,
        context: any,
        instructionIndex: number,
        txType: 'add_liquidity' | 'remove_liquidity'
    ): SolanaDexTrade {
        // 实现 DAMM V1 流动性交易创建
        return {} as SolanaDexTrade;
    }

    private createOptimizedBootstrapPool(
        transaction: ConfirmedTransaction,
        eventData: any,
        config: any,
        context: any
    ): SolanaDexNewPool {
        // 实现 Bootstrap 池子创建
        return {} as SolanaDexNewPool;
    }
}

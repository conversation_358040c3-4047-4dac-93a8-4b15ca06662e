import * as anchor from '@coral-xyz/anchor';
import { PublicKey } from '@solana/web3.js';
import { BN } from '@coral-xyz/anchor';
import { ConfirmedTransaction } from '../../types';
import { idlConfigManager } from '../idl/idlConfigManager';

/**
 * Meteora 指令数据提取器
 * 通过 IDL 解析指令，获取 tokenA、tokenB 等数据
 */
export class MeteoraInstructionDataExtractor {
    private supportedPrograms: string[];
    private programCache = new Map<string, anchor.Program>();
    
    // 程序 ID 常量
    private readonly DLMM_PROGRAM_ID = 'LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo';
    private readonly DAMM_V1_PROGRAM_ID = 'Eo7WjKq67rjJQSZxS6z3YkapzY3eMj6Xy8X5EQVn5UaB';

    constructor(supportedPrograms: string[]) {
        this.supportedPrograms = supportedPrograms;
    }

    /**
     * 从交易中提取指令数据，获取代币地址等信息
     */
    async extractInstructionData(transaction: ConfirmedTransaction): Promise<InstructionDataMap> {
        const instructionDataMap = new Map<string, InstructionData>();
        
        const instructions = transaction.transaction?.transaction?.message?.instructions || [];
        const accountKeys = transaction.transaction?.transaction?.message?.accountKeys || [];
        
        for (let i = 0; i < instructions.length; i++) {
            const instruction = instructions[i];
            const programAccount = accountKeys[instruction.programIdIndex];
            
            if (this.supportedPrograms.includes(programAccount)) {
                try {
                    const instructionData = await this.parseInstruction(
                        instruction, 
                        accountKeys, 
                        programAccount, 
                        i
                    );
                    
                    if (instructionData) {
                        instructionDataMap.set(i.toString(), instructionData);
                    }
                } catch (error) {
                    console.warn(`解析指令 ${i} 失败:`, error);
                }
            }
        }
        
        return instructionDataMap;
    }

    /**
     * 解析单个指令
     */
    private async parseInstruction(
        instruction: any,
        accountKeys: string[],
        programId: string,
        instructionIndex: number
    ): Promise<InstructionData | null> {
        try {
            const program = await this.getProgram(programId);
            if (!program) return null;
            
            // 解码指令数据
            const instructionName = this.getInstructionName(instruction.data, program);
            if (!instructionName) return null;
            
            // 根据指令类型和程序类型提取数据
            if (programId === this.DLMM_PROGRAM_ID) {
                return this.extractDLMMInstructionData(instruction, accountKeys, instructionName, program);
            } else if (programId === this.DAMM_V1_PROGRAM_ID) {
                return this.extractDAMMV1InstructionData(instruction, accountKeys, instructionName, program);
            }
            
            return null;
        } catch (error) {
            console.warn(`解析指令失败:`, error);
            return null;
        }
    }

    /**
     * 提取 DLMM 指令数据
     */
    private extractDLMMInstructionData(
        instruction: any,
        accountKeys: string[],
        instructionName: string,
        program: anchor.Program
    ): InstructionData | null {
        try {
            // 获取指令定义
            const instructionDef = program.idl.instructions.find(
                (inst: any) => inst.name === instructionName
            );
            
            if (!instructionDef) return null;
            
            const accounts = instruction.accounts || [];
            const accountMap = new Map<string, string>();
            
            // 映射账户名称到地址
            instructionDef.accounts.forEach((accountDef: any, index: number) => {
                if (index < accounts.length && accounts[index] < accountKeys.length) {
                    accountMap.set(accountDef.name, accountKeys[accounts[index]]);
                }
            });
            
            // 根据指令类型提取特定数据
            switch (instructionName) {
                case 'swap':
                    return {
                        instructionName,
                        programId: this.DLMM_PROGRAM_ID,
                        poolAddress: accountMap.get('lbPair') || 'unknown',
                        tokenXMint: accountMap.get('tokenXMint') || 'unknown',
                        tokenYMint: accountMap.get('tokenYMint') || 'unknown',
                        reserveX: accountMap.get('reserveX') || 'unknown',
                        reserveY: accountMap.get('reserveY') || 'unknown',
                        userTokenIn: accountMap.get('userTokenIn') || 'unknown',
                        userTokenOut: accountMap.get('userTokenOut') || 'unknown',
                        user: this.extractUserFromAccounts(accountMap),
                        accountMap
                    };
                    
                case 'initializeLbPair':
                    return {
                        instructionName,
                        programId: this.DLMM_PROGRAM_ID,
                        poolAddress: accountMap.get('lbPair') || 'unknown',
                        tokenXMint: accountMap.get('tokenMintX') || 'unknown',
                        tokenYMint: accountMap.get('tokenMintY') || 'unknown',
                        reserveX: accountMap.get('reserveX') || 'unknown',
                        reserveY: accountMap.get('reserveY') || 'unknown',
                        funder: accountMap.get('funder') || 'unknown',
                        accountMap
                    };
                    
                case 'addLiquidity':
                case 'removeLiquidity':
                    return {
                        instructionName,
                        programId: this.DLMM_PROGRAM_ID,
                        poolAddress: accountMap.get('lbPair') || 'unknown',
                        tokenXMint: accountMap.get('tokenXMint') || 'unknown',
                        tokenYMint: accountMap.get('tokenYMint') || 'unknown',
                        reserveX: accountMap.get('reserveX') || 'unknown',
                        reserveY: accountMap.get('reserveY') || 'unknown',
                        position: accountMap.get('position') || 'unknown',
                        user: this.extractUserFromAccounts(accountMap),
                        accountMap
                    };
                    
                default:
                    return {
                        instructionName,
                        programId: this.DLMM_PROGRAM_ID,
                        poolAddress: accountMap.get('lbPair') || 'unknown',
                        accountMap
                    };
            }
        } catch (error) {
            console.warn('提取 DLMM 指令数据失败:', error);
            return null;
        }
    }

    /**
     * 提取 DAMM V1 指令数据
     */
    private extractDAMMV1InstructionData(
        instruction: any,
        accountKeys: string[],
        instructionName: string,
        program: anchor.Program
    ): InstructionData | null {
        try {
            // DAMM V1 的指令解析逻辑
            const instructionDef = program.idl.instructions.find(
                (inst: any) => inst.name === instructionName
            );
            
            if (!instructionDef) return null;
            
            const accounts = instruction.accounts || [];
            const accountMap = new Map<string, string>();
            
            // 映射账户名称到地址
            instructionDef.accounts.forEach((accountDef: any, index: number) => {
                if (index < accounts.length && accounts[index] < accountKeys.length) {
                    accountMap.set(accountDef.name, accountKeys[accounts[index]]);
                }
            });
            
            // DAMM V1 的账户结构可能不同，需要根据实际 IDL 调整
            return {
                instructionName,
                programId: this.DAMM_V1_PROGRAM_ID,
                poolAddress: accountMap.get('pool') || accountMap.get('ammPool') || 'unknown',
                tokenAMint: accountMap.get('tokenAMint') || accountMap.get('tokenMintA') || 'unknown',
                tokenBMint: accountMap.get('tokenBMint') || accountMap.get('tokenMintB') || 'unknown',
                tokenAVault: accountMap.get('tokenAVault') || accountMap.get('aVault') || 'unknown',
                tokenBVault: accountMap.get('tokenBVault') || accountMap.get('bVault') || 'unknown',
                user: this.extractUserFromAccounts(accountMap),
                accountMap
            };
        } catch (error) {
            console.warn('提取 DAMM V1 指令数据失败:', error);
            return null;
        }
    }

    /**
     * 从账户映射中提取用户地址
     */
    private extractUserFromAccounts(accountMap: Map<string, string>): string {
        // 尝试多种可能的用户账户名称
        const userAccountNames = ['user', 'owner', 'authority', 'signer', 'funder'];
        
        for (const name of userAccountNames) {
            const address = accountMap.get(name);
            if (address && address !== 'unknown') {
                return address;
            }
        }
        
        return 'unknown';
    }

    /**
     * 获取指令名称
     */
    private getInstructionName(instructionData: Uint8Array, program: anchor.Program): string | null {
        try {
            // 使用 Anchor 的指令解码器
            const decoded = program.coder.instruction.decode(instructionData);
            return decoded?.name || null;
        } catch (error) {
            // 如果解码失败，尝试其他方法
            console.warn('指令解码失败:', error);
            return null;
        }
    }

    /**
     * 获取程序实例
     */
    private async getProgram(programIdString: string): Promise<anchor.Program | null> {
        if (this.programCache.has(programIdString)) {
            return this.programCache.get(programIdString)!;
        }

        try {
            const config = idlConfigManager.getConfig(programIdString);
            if (!config) return null;

            const programId = new PublicKey(programIdString);
            const program = new anchor.Program(config.idl, programId);
            this.programCache.set(programIdString, program);
            return program;
        } catch (error) {
            console.error(`创建程序实例失败 ${programIdString}:`, error);
            return null;
        }
    }

    /**
     * 根据池子地址查找对应的指令数据
     */
    findInstructionDataByPool(instructionDataMap: InstructionDataMap, poolAddress: string): InstructionData | null {
        for (const [index, data] of instructionDataMap) {
            if (data.poolAddress === poolAddress) {
                return data;
            }
        }
        return null;
    }

    /**
     * 获取代币地址（优先使用指令数据）
     */
    getTokenAddresses(instructionDataMap: InstructionDataMap, poolAddress: string): {
        token0Address: string;
        token1Address: string;
    } {
        const instructionData = this.findInstructionDataByPool(instructionDataMap, poolAddress);
        
        if (instructionData) {
            if (instructionData.programId === this.DLMM_PROGRAM_ID) {
                return {
                    token0Address: instructionData.tokenXMint || 'unknown',
                    token1Address: instructionData.tokenYMint || 'unknown'
                };
            } else if (instructionData.programId === this.DAMM_V1_PROGRAM_ID) {
                return {
                    token0Address: instructionData.tokenAMint || 'unknown',
                    token1Address: instructionData.tokenBMint || 'unknown'
                };
            }
        }
        
        return { token0Address: 'unknown', token1Address: 'unknown' };
    }
}

// 类型定义
export interface InstructionData {
    instructionName: string;
    programId: string;
    poolAddress: string;
    
    // DLMM 特有字段
    tokenXMint?: string;
    tokenYMint?: string;
    reserveX?: string;
    reserveY?: string;
    
    // DAMM V1 特有字段
    tokenAMint?: string;
    tokenBMint?: string;
    tokenAVault?: string;
    tokenBVault?: string;
    
    // 通用字段
    user?: string;
    userTokenIn?: string;
    userTokenOut?: string;
    position?: string;
    funder?: string;
    
    // 完整的账户映射
    accountMap: Map<string, string>;
}

export type InstructionDataMap = Map<string, InstructionData>;

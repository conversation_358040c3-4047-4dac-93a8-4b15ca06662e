import { MeteoraEventParser } from '../parsers/meteora/meteoraEventParser';
import { ConfirmedTransaction } from '../types';

/**
 * 演示如何从 Meteora 交易中解析 token0Address、token1Address 和池子余额
 */
class MeteoraTokenAddressExample {
    private parser: MeteoraEventParser;

    constructor() {
        // 支持的 Meteora 程序 ID
        const supportedPrograms = [
            'LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo', // Meteora DLMM
            'Eo7WjKq67rjJQSZxS6z3YkapzY3eMj6Xy8X5EQVn5UaB', // Meteora DAMM V1
        ];
        
        this.parser = new MeteoraEventParser(supportedPrograms);
    }

    /**
     * 创建模拟的 Meteora 交易数据
     */
    private createMockTransaction(): ConfirmedTransaction {
        return {
            slot: *********,
            transaction: {
                meta: {
                    err: null,
                    innerInstructions: [
                        {
                            index: 0,
                            instructions: [
                                {
                                    programIdIndex: 1,
                                    accounts: [0], // 事件 PDA 账户
                                    data: new Uint8Array([/* 模拟事件数据 */])
                                }
                            ]
                        }
                    ],
                    postTokenBalances: [
                        {
                            accountIndex: 2,
                            mint: 'So11111111111111111111111111111111111111112', // SOL
                            owner: 'pool_address_123',
                            uiTokenAmount: {
                                amount: '**********0',
                                decimals: 9,
                                uiAmount: 50.0,
                                uiAmountString: '50.0'
                            }
                        },
                        {
                            accountIndex: 3,
                            mint: 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v', // USDC
                            owner: 'pool_address_123',
                            uiTokenAmount: {
                                amount: '**********',
                                decimals: 6,
                                uiAmount: 5000.0,
                                uiAmountString: '5000.0'
                            }
                        }
                    ],
                    preTokenBalances: []
                },
                transaction: {
                    message: {
                        accountKeys: [
                            'pool_address_123',                                    // 0: 池子地址
                            'LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo',        // 1: Meteora DLMM 程序
                            'token0_vault_address',                               // 2: Token0 Vault
                            'token1_vault_address',                               // 3: Token1 Vault
                            'user_address',                                       // 4: 用户地址
                            'other_account_1',                                    // 5
                            'other_account_2',                                    // 6
                            'token0_vault_address',                               // 7: Token0 Vault (指令账户6)
                            'token1_vault_address',                               // 8: Token1 Vault (指令账户7)
                        ],
                        instructions: [
                            {
                                programIdIndex: 1,
                                accounts: [0, 1, 2, 3, 4, 5, 6, 7, 8], // 9个账户，满足 > 8 的条件
                                data: new Uint8Array([/* 指令数据 */])
                            }
                        ]
                    },
                    signatures: [new Uint8Array([/* 签名数据 */])]
                }
            }
        };
    }

    /**
     * 演示代币地址解析
     */
    async demonstrateTokenAddressParsing(): Promise<void> {
        console.log('🔍 演示 Meteora 代币地址解析');
        console.log('=====================================');

        const mockTransaction = this.createMockTransaction();

        // 1. 演示 SPL Token 映射构建
        console.log('\n1️⃣ 构建 SPL Token 映射:');
        const splTokenMap = this.buildSplTokenMapDemo(mockTransaction);
        console.log('SPL Token 映射:', Object.fromEntries(splTokenMap));

        // 2. 演示池子余额映射构建
        console.log('\n2️⃣ 构建池子余额映射:');
        const reservesMap = this.buildReservesMapDemo(mockTransaction);
        console.log('池子余额映射:', Object.fromEntries(reservesMap));

        // 3. 演示代币地址获取
        console.log('\n3️⃣ 获取代币地址:');
        const { token0Address, token1Address } = this.getTokenAddressesDemo(
            'pool_address_123', 
            mockTransaction
        );
        console.log(`Token0 地址: ${token0Address}`);
        console.log(`Token1 地址: ${token1Address}`);

        // 4. 演示完整的事件解析（如果有真实的事件数据）
        console.log('\n4️⃣ 完整事件解析结果:');
        try {
            const events = await this.parser.parseTransaction(mockTransaction);
            console.log(`解析到 ${events.length} 个事件`);
            
            events.forEach((event, index) => {
                if ('txType' in event) {
                    // 这是一个交易事件
                    console.log(`事件 ${index + 1} (交易):`);
                    console.log(`  - 池子地址: ${event.poolAddress}`);
                    console.log(`  - Token0: ${event.token0Address}`);
                    console.log(`  - Token1: ${event.token1Address}`);
                    console.log(`  - Token0 余额: ${event.token0Reserve}`);
                    console.log(`  - Token1 余额: ${event.token1Reserve}`);
                    console.log(`  - 交易类型: ${event.txType}`);
                } else {
                    // 这是一个新池事件
                    console.log(`事件 ${index + 1} (新池):`);
                    console.log(`  - 池子地址: ${event.pairAddress}`);
                    console.log(`  - Token0: ${event.token0Address}`);
                    console.log(`  - Token1: ${event.token1Address}`);
                }
            });
        } catch (error) {
            console.log('注意: 由于使用模拟数据，事件解析可能失败，这是正常的');
            console.log('在真实环境中，这些方法将正确解析代币地址和余额');
        }
    }

    /**
     * 演示 SPL Token 映射构建（复制自 MeteoraEventParser 的私有方法）
     */
    private buildSplTokenMapDemo(transaction: ConfirmedTransaction): Map<string, {mint: string, decimals: number}> {
        const splTokenAddresses = new Map<string, {mint: string, decimals: number}>();
        
        const postTokenBalances = transaction.transaction?.meta?.postTokenBalances || [];
        for (const item of postTokenBalances) {
            if (item.mint && item.uiTokenAmount?.decimals !== undefined) {
                splTokenAddresses.set(item.mint, {
                    mint: item.mint,
                    decimals: item.uiTokenAmount.decimals
                });
            }
        }
        
        return splTokenAddresses;
    }

    /**
     * 演示池子余额映射构建
     */
    private buildReservesMapDemo(transaction: ConfirmedTransaction): Map<string, {token0Reserve: string, token1Reserve: string}> {
        const reservesMap = new Map<string, {token0Reserve: string, token1Reserve: string}>();
        
        const instructions = transaction.transaction?.transaction?.message?.instructions || [];
        const accountKeys = transaction.transaction?.transaction?.message?.accountKeys || [];
        
        for (const instruction of instructions) {
            const programAccount = accountKeys[instruction.programIdIndex];
            if (programAccount === 'LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo') {
                if (instruction.accounts && instruction.accounts.length > 8) {
                    const poolAddress = accountKeys[instruction.accounts[0]];
                    const token0VaultAddress = accountKeys[instruction.accounts[6]];
                    const token1VaultAddress = accountKeys[instruction.accounts[7]];
                    
                    const reserves = this.getPoolReservesDemo(
                        transaction, 
                        poolAddress, 
                        token0VaultAddress, 
                        token1VaultAddress
                    );
                    
                    reservesMap.set(poolAddress, reserves);
                }
            }
        }
        
        return reservesMap;
    }

    /**
     * 演示池子余额获取
     */
    private getPoolReservesDemo(
        transaction: ConfirmedTransaction,
        poolAddress: string,
        token0VaultAddress: string,
        token1VaultAddress: string
    ): {token0Reserve: string, token1Reserve: string} {
        let token0Reserve = '0';
        let token1Reserve = '0';
        
        const postTokenBalances = transaction.transaction?.meta?.postTokenBalances || [];
        const accountKeys = transaction.transaction?.transaction?.message?.accountKeys || [];
        
        for (const balance of postTokenBalances) {
            if (balance.owner === poolAddress && balance.uiTokenAmount?.uiAmountString) {
                const accountAddress = accountKeys[balance.accountIndex];
                
                if (accountAddress === token0VaultAddress) {
                    token0Reserve = balance.uiTokenAmount.uiAmountString;
                } else if (accountAddress === token1VaultAddress) {
                    token1Reserve = balance.uiTokenAmount.uiAmountString;
                }
            }
        }
        
        return { token0Reserve, token1Reserve };
    }

    /**
     * 演示代币地址获取
     */
    private getTokenAddressesDemo(
        poolAddress: string, 
        transaction: ConfirmedTransaction
    ): {token0Address: string, token1Address: string} {
        const instructions = transaction.transaction?.transaction?.message?.instructions || [];
        const accountKeys = transaction.transaction?.transaction?.message?.accountKeys || [];
        
        for (const instruction of instructions) {
            const programAccount = accountKeys[instruction.programIdIndex];
            if (programAccount === 'LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo') {
                if (instruction.accounts && instruction.accounts.length > 8) {
                    const instructionPoolAddress = accountKeys[instruction.accounts[0]];
                    
                    if (instructionPoolAddress === poolAddress) {
                        const token0VaultAddress = accountKeys[instruction.accounts[6]];
                        const token1VaultAddress = accountKeys[instruction.accounts[7]];
                        
                        const token0Address = this.getMintFromMetaDemo(transaction, token0VaultAddress);
                        const token1Address = this.getMintFromMetaDemo(transaction, token1VaultAddress);
                        
                        return { token0Address, token1Address };
                    }
                }
            }
        }
        
        return { token0Address: 'unknown', token1Address: 'unknown' };
    }

    /**
     * 演示从元数据获取 mint 地址
     */
    private getMintFromMetaDemo(transaction: ConfirmedTransaction, ataAddress: string): string {
        const accountKeys = transaction.transaction?.transaction?.message?.accountKeys || [];
        
        const postTokenBalances = transaction.transaction?.meta?.postTokenBalances || [];
        for (const balance of postTokenBalances) {
            const ownerAddress = accountKeys[balance.accountIndex];
            if (ownerAddress === ataAddress && balance.mint) {
                return balance.mint;
            }
        }
        
        return 'unknown';
    }
}

// 如果直接运行此文件，执行演示
if (require.main === module) {
    const example = new MeteoraTokenAddressExample();
    example.demonstrateTokenAddressParsing().catch(console.error);
}

export { MeteoraTokenAddressExample };

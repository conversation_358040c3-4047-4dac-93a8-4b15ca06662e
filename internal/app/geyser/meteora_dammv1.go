package geyser

import (
	"debot-backend/internal/app/geyser/grpc/proto"
	"debot-backend/internal/common/consts"
	"debot-backend/internal/common/enums"
	"debot-backend/internal/common/utils"
	"debot-backend/internal/service"
	"debot-backend/pkg/helius"
	"debot-backend/pkg/log"
	"fmt"
	"strings"

	"github.com/mr-tron/base58"
	"github.com/shopspring/decimal"
)

func (c *Client) SetDAMMV1Callback(tradeHandler helius.ConsumeMeteoraTradeHandler, newPoolHandler helius.ConsumeMeteoraNewPoolHandler, poolReader helius.PoolInfoReader) {
	c.dexName = consts.MeteoraDAMMV1Dex
	c.meteoraTradeHandler = tradeHandler
	c.meteoraNewPoolHandler = newPoolHandler
	c.poolReader = poolReader
}

func (c *Client) parserMeteoraDAMMV1(subscribeUpdateTransaction *proto.SubscribeUpdate_Transaction) error {
	if subscribeUpdateTransaction.Transaction.Transaction.Meta.Err != nil {
		return nil
	}
	var resultTradeList []helius.SolanaDexTrade
	transaction := subscribeUpdateTransaction.Transaction.Transaction
	meta := transaction.Meta
	allInstructions := c.GetTransactionAllInstructions(transaction, meta)
	dexSource := c.GetTransactionDexSource(allInstructions, transaction)
	balanceChange := c.GetBalanceChange(subscribeUpdateTransaction.Transaction.Transaction)
	resultTradeList = append(resultTradeList, c.parserMeteoraDAMMV1Trade(subscribeUpdateTransaction, allInstructions, balanceChange, dexSource)...)
	if len(resultTradeList) > 0 {
		//log.Log.Println(resultTradeList)
		_ = c.meteoraTradeHandler(resultTradeList)
	}
	var resultPool []helius.SolanaDexNewPool
	if c.dexName == consts.MeteoraDAMMV1Dex {
		resultPool = append(resultPool, c.parserMeteoraDAMMV1NewPool(subscribeUpdateTransaction, allInstructions)...)
	}

	if len(resultPool) > 0 {
		log.Log.Println(resultPool)
		_ = c.meteoraNewPoolHandler(resultPool)
	}
	return nil
}

func (c *Client) parserMeteoraDAMMV1NewPool(subscribeUpdateTransaction *proto.SubscribeUpdate_Transaction, allInstructions []*proto.CompiledInstructionIndex) []helius.SolanaDexNewPool {
	var result []helius.SolanaDexNewPool
	accountKey := subscribeUpdateTransaction.Transaction.Transaction.Transaction.Message.AccountKeys
	transaction := subscribeUpdateTransaction.Transaction.Transaction
	txHash := base58.Encode(transaction.Signature)
	meta := transaction.Meta
	slot := int64(subscribeUpdateTransaction.Transaction.Slot)
	for _, instruction := range allInstructions {
		instructionProgramID, _ := meta.ProgramAddress(accountKey, byte(instruction.ProgramIdIndex))
		if instructionProgramID == consts.MeteoraDAMMV1BondingCurve {
			instructionData := base58.Encode(instruction.Data)
			if strings.HasPrefix(instructionData, consts.MeteoraDammV1Create) {
				creator, _ := meta.ProgramAddress(accountKey, instruction.Accounts[22])
				pairAddress, _ := meta.ProgramAddress(accountKey, instruction.Accounts[4])
				token0, _ := meta.ProgramAddress(accountKey, instruction.Accounts[7])
				token1, _ := meta.ProgramAddress(accountKey, instruction.Accounts[8])
				result = append(result, helius.SolanaDexNewPool{
					PairAddress:      pairAddress,
					DexName:          consts.MeteoraDAMMV1Dex,
					PairName:         fmt.Sprintf("%s-%s", token0, token1),
					Token0Address:    token0,
					Token1Address:    token1,
					DeployerAddress:  creator,
					CreationTxHash:   txHash,
					InstructionIndex: instruction.Index,
					BlockNumber:      uint64(slot),
					CreatedAt:        utils.GetNowUnix(),
				})
			}
		}
	}
	return result
}

func (c *Client) parserMeteoraDAMMV1Trade(subscribeUpdateTransaction *proto.SubscribeUpdate_Transaction, allInstructions []*proto.CompiledInstructionIndex, balanceChange *BalanceChange, dexSource string) []helius.SolanaDexTrade {
	var result []helius.SolanaDexTrade
	accountKey := subscribeUpdateTransaction.Transaction.Transaction.Transaction.Message.AccountKeys
	transaction := subscribeUpdateTransaction.Transaction.Transaction
	txHash := base58.Encode(transaction.Signature)
	meta := transaction.Meta
	slot := int64(subscribeUpdateTransaction.Transaction.Slot)
	for _, instruction := range allInstructions {
		instructionProgramID, _ := meta.ProgramAddress(accountKey, byte(instruction.ProgramIdIndex))
		if instructionProgramID != consts.MeteoraDAMMV1 {
			continue
		}
		instructionData := base58.Encode(instruction.Data)
		instructionAccountsLen := len(instruction.Accounts)
		if instructionAccountsLen == 15 && strings.HasPrefix(instructionData, consts.MeteoralDammV1Swap) {
			poolAddress, _ := meta.ProgramAddress(accountKey, instruction.Accounts[0])
			_, _ = c.poolReader.GetMeteoraDAMMV1PoolInfo(poolAddress) //初始化池子数据
			token0PoolAccount, _ := meta.ProgramAddress(accountKey, instruction.Accounts[5])
			token1PoolAccount, _ := meta.ProgramAddress(accountKey, instruction.Accounts[6])
			token0Address := c.GetMintFromMeta(subscribeUpdateTransaction.Transaction.Transaction, token0PoolAccount)
			token1Address := c.GetMintFromMeta(subscribeUpdateTransaction.Transaction.Transaction, token1PoolAccount)
			//traderAddress, _ := meta.ProgramAddress(accountKey, instruction.Accounts[12])
			traderAddress, _ := meta.ProgramAddress(accountKey, 0)

			poolBalanceChangeToken0 := balanceChange.GetTokenBalanceByAccount(token0Address, token0PoolAccount)
			token0Raw := poolBalanceChangeToken0.TokenAfterAmount.Sub(poolBalanceChangeToken0.TokenBeforeAmount)
			var buyTokenAddress string
			if token0Raw.Sign() < 0 { // 池子里减少，说明是买的token0，否则就是买的token1
				buyTokenAddress = token0Address
			} else {
				buyTokenAddress = token1Address
			}
			txType := enums.TxTypeBuy
			if consts.IsChainNativeOrStableCoin(consts.Solana, buyTokenAddress) { // 如果用户买入的稳定币，就是卖出token操作
				txType = enums.TxTypeSell
			}
			trade := c.GetMeteoraDammV1SwapTrade(txType, balanceChange, token0Address, token0PoolAccount, token1Address, token1PoolAccount, poolAddress, txHash, slot, instruction, traderAddress, dexSource)
			result = append(result, trade)
		} else if instructionAccountsLen == 16 && strings.HasPrefix(instructionData, consts.MeteoralDammV1AddLiquidity) {
			poolAddress, _ := meta.ProgramAddress(accountKey, instruction.Accounts[0])
			traderAddress, _ := meta.ProgramAddress(accountKey, instruction.Accounts[13])
			token0PoolAccount, _ := meta.ProgramAddress(accountKey, instruction.Accounts[9])
			token1PoolAccount, _ := meta.ProgramAddress(accountKey, instruction.Accounts[10])
			token0Address := c.GetMintFromMeta(subscribeUpdateTransaction.Transaction.Transaction, token0PoolAccount)
			token1Address := c.GetMintFromMeta(subscribeUpdateTransaction.Transaction.Transaction, token1PoolAccount)
			trade := c.GetMeteoraDammV1SwapTrade(enums.TxTypeAddLiquidity, balanceChange, token0Address, token0PoolAccount, token1Address, token1PoolAccount, poolAddress, txHash, slot, instruction, traderAddress, dexSource)
			result = append(result, trade)
		} else if instructionAccountsLen == 16 && strings.HasPrefix(instructionData, consts.MeteoralDammV1AddImLiquidity) {
			poolAddress, _ := meta.ProgramAddress(accountKey, instruction.Accounts[0])
			traderAddress, _ := meta.ProgramAddress(accountKey, instruction.Accounts[13])
			token0PoolAccount, _ := meta.ProgramAddress(accountKey, instruction.Accounts[9])
			token1PoolAccount, _ := meta.ProgramAddress(accountKey, instruction.Accounts[10])
			token0Address := c.GetMintFromMeta(subscribeUpdateTransaction.Transaction.Transaction, token0PoolAccount)
			token1Address := c.GetMintFromMeta(subscribeUpdateTransaction.Transaction.Transaction, token1PoolAccount)
			trade := c.GetMeteoraDammV1SwapTrade(enums.TxTypeAddLiquidity, balanceChange, token0Address, token0PoolAccount, token1Address, token1PoolAccount, poolAddress, txHash, slot, instruction, traderAddress, dexSource)
			result = append(result, trade)
		} else if instructionAccountsLen == 16 && strings.HasPrefix(instructionData, consts.MeteoralDammV1RemoveLiquidity) {
			poolAddress, _ := meta.ProgramAddress(accountKey, instruction.Accounts[0])
			traderAddress, _ := meta.ProgramAddress(accountKey, instruction.Accounts[13])
			token0PoolAccount, _ := meta.ProgramAddress(accountKey, instruction.Accounts[9])
			token1PoolAccount, _ := meta.ProgramAddress(accountKey, instruction.Accounts[10])
			token0Address := c.GetMintFromMeta(subscribeUpdateTransaction.Transaction.Transaction, token0PoolAccount)
			token1Address := c.GetMintFromMeta(subscribeUpdateTransaction.Transaction.Transaction, token1PoolAccount)
			trade := c.GetMeteoraDammV1SwapTrade(enums.TxTypeRemoveLiquidity, balanceChange, token0Address, token0PoolAccount, token1Address, token1PoolAccount, poolAddress, txHash, slot, instruction, traderAddress, dexSource)
			result = append(result, trade)
		} else if instructionAccountsLen == 15 && strings.HasPrefix(instructionData, consts.MeteoralDammV1RemoveSingleLiquidity) {
			poolAddress, _ := meta.ProgramAddress(accountKey, instruction.Accounts[0])
			traderAddress, _ := meta.ProgramAddress(accountKey, instruction.Accounts[12])
			token0PoolAccount, _ := meta.ProgramAddress(accountKey, instruction.Accounts[9])
			token1PoolAccount, _ := meta.ProgramAddress(accountKey, instruction.Accounts[10])
			token0Address := c.GetMintFromMeta(subscribeUpdateTransaction.Transaction.Transaction, token0PoolAccount)
			token1Address := c.GetMintFromMeta(subscribeUpdateTransaction.Transaction.Transaction, token1PoolAccount)
			trade := c.GetMeteoraDammV1SwapTrade(enums.TxTypeRemoveLiquidity, balanceChange, token0Address, token0PoolAccount, token1Address, token1PoolAccount, poolAddress, txHash, slot, instruction, traderAddress, dexSource)
			result = append(result, trade)
		}
	}
	return result
}

func (c *Client) GetMeteoraDammV1SwapTrade(txType enums.TxType, balanceChange *BalanceChange, token0Address string, token0PoolAccount string, token1Address string, token1PoolAccount string, poolAddress string, txHash string, slot int64, instruction *proto.CompiledInstructionIndex, traderAddress string, dexSource string) helius.SolanaDexTrade {
	poolBalanceChangeToken0 := balanceChange.GetTokenBalanceByAccount(token0Address, token0PoolAccount)
	poolBalanceChangeToken1 := balanceChange.GetTokenBalanceByAccount(token1Address, token1PoolAccount)
	token0Raw := poolBalanceChangeToken0.TokenAfterAmount.Sub(poolBalanceChangeToken0.TokenBeforeAmount)
	token1Raw := poolBalanceChangeToken1.TokenAfterAmount.Sub(poolBalanceChangeToken1.TokenBeforeAmount)
	token0Amount := token0Raw.Abs()
	token1Amount := token1Raw.Abs()
	return c.GetMeteoraDammV1Trade(txType, token0Amount, token1Amount, poolAddress, txHash, slot, instruction, traderAddress, token0Address, token1Address, poolBalanceChangeToken0, poolBalanceChangeToken1, dexSource)
}

func (c *Client) GetMeteoraDammV1Trade(txType enums.TxType, token0Amount decimal.Decimal, token1Amount decimal.Decimal, poolAddress string, txHash string, slot int64, instruction *proto.CompiledInstructionIndex, traderAddress string, token0Address string, token1Address string, poolBalanceChangeToken0 TokenBalanceChangeInfo, poolBalanceChangeToken1 TokenBalanceChangeInfo, dexSource string) helius.SolanaDexTrade {
	var token0UnitPrice decimal.Decimal
	if token0Amount.Cmp(decimal.Zero) > 0 {
		token0UnitPrice = token1Amount.Div(token0Amount).Round(18)
	} else {
		token0UnitPrice = decimal.Zero
	}
	trade := helius.SolanaDexTrade{
		DexName:          consts.MeteoraDAMMV1Dex,
		PoolAddress:      poolAddress,
		TxHash:           txHash,
		TxType:           txType,
		Slot:             slot,
		InstructionIndex: instruction.Index,
		Timestamp:        utils.GetNowUnix(),
		TraderAddress:    traderAddress,
		Token0Amount:     token0Amount.String(),
		Token1Amount:     token1Amount.String(),
		Token0UnitPrice:  token0UnitPrice.String(),
		Token0Address:    token0Address,
		Token1Address:    token1Address,
		Token0Reserve:    poolBalanceChangeToken0.TokenAfterAmount.String(),
		Token1Reserve:    poolBalanceChangeToken1.TokenAfterAmount.String(),
		DexSource:        dexSource,
	}
	return trade
}

type MeteoraDammv1Sync struct {
	client *Client
}

func NewMeteoraDammv1Sync(grpcAddress string, token string) *MeteoraDammv1Sync {
	client, _ := NewClient(grpcAddress, token)
	return &MeteoraDammv1Sync{client: client}
}

func (c *MeteoraDammv1Sync) Start() {
	c.client.SetDAMMV1Callback(service.MeteoraService.SaveTrade, service.MeteoraService.SaveNewPool, service.PairService)
	c.client.SubscribeAccount(consts.MeteoraDAMMV1)
}

func (c *MeteoraDammv1Sync) Close() error {
	c.client.Stop()
	return nil
}

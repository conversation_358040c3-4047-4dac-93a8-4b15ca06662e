package geyser

import (
	"debot-backend/internal/app/geyser/grpc/proto"
	"debot-backend/internal/common/consts"
	"debot-backend/internal/common/enums"
	"debot-backend/internal/common/utils"
	"debot-backend/internal/service"
	"debot-backend/pkg/helius"
	"debot-backend/pkg/log"
	"debot-backend/pkg/meteora"
	"fmt"
	"github.com/mr-tron/base58"
	"github.com/shopspring/decimal"
	"strings"
	"time"
)

func (c *Client) SetDLMMCallback(tradeHandler helius.ConsumeMeteoraTradeHandler, newPoolHandler helius.ConsumeMeteoraNewPoolHandler, poolReader helius.PoolInfoReader) {
	c.dexName = consts.MeteoraDLMMDex
	c.meteoraTradeHandler = tradeHandler
	c.meteoraNewPoolHandler = newPoolHandler
	c.poolReader = poolReader
}

func (c *Client) parserMeteoraDLMM(subscribeUpdateTransaction *proto.SubscribeUpdate_Transaction) error {
	if subscribeUpdateTransaction.Transaction.Transaction.Meta.Err != nil {
		return nil
	}

	accountKey := subscribeUpdateTransaction.Transaction.Transaction.Transaction.Message.AccountKeys
	meta := subscribeUpdateTransaction.Transaction.Transaction.Meta
	signature := base58.Encode(subscribeUpdateTransaction.Transaction.Transaction.Signature)
	slot := int64(subscribeUpdateTransaction.Transaction.Slot)
	innerInstructions := subscribeUpdateTransaction.Transaction.Transaction.Meta.InnerInstructions

	splTokenAddresses := make(map[string]tokenInfo)
	for _, item := range subscribeUpdateTransaction.Transaction.Transaction.Meta.PostTokenBalances {
		splTokenAddresses[item.Mint] = tokenInfo{
			Mint:     item.Mint,
			Decimals: int(item.UiTokenAmount.Decimals),
		}
	}

	reservesMap := make(map[string][]string)
	for _, instruction := range subscribeUpdateTransaction.Transaction.Transaction.Transaction.Message.Instructions {
		programAccount, _ := meta.ProgramAddress(accountKey, byte(instruction.ProgramIdIndex))
		if programAccount == consts.MeteoraDLMM {
			instructionData := base58.Encode(instruction.Data)
			if strings.HasPrefix(instructionData, consts.MeteoralDlmmTradeInstructionForLq) {
				if len(instruction.Accounts) > 8 {
					poolAddress, _ := meta.ProgramAddress(accountKey, instruction.Accounts[0])
					token0Address, _ := meta.ProgramAddress(accountKey, instruction.Accounts[6])
					token1Address, _ := meta.ProgramAddress(accountKey, instruction.Accounts[7])
					// 流动性数据
					token0AccountIndex := instruction.Accounts[2]
					token1AccountIndex := instruction.Accounts[3]
					token0Reserve, token1Reserve := c.getPoolReserves(meta, token0AccountIndex, token1AccountIndex, token0Address, token1Address)
					reservesMap[poolAddress] = []string{token0Reserve, token1Reserve}
				}
			}
		}
	}
	for _, innerInstruction := range innerInstructions {
		for _, instruction := range innerInstruction.Instructions {
			programAccount, _ := meta.ProgramAddress(accountKey, byte(instruction.ProgramIdIndex))
			if programAccount == consts.MeteoraDLMM {
				instructionData := base58.Encode(instruction.Data)
				if strings.HasPrefix(instructionData, consts.MeteoralDlmmTradeInstructionForLq) {
					if len(instruction.Accounts) > 8 {
						poolAddress, _ := meta.ProgramAddress(accountKey, instruction.Accounts[0])
						token0Address, _ := meta.ProgramAddress(accountKey, instruction.Accounts[6])
						token1Address, _ := meta.ProgramAddress(accountKey, instruction.Accounts[7])
						// 流动性数据
						token0AccountIndex := instruction.Accounts[2]
						token1AccountIndex := instruction.Accounts[3]
						token0Reserve, token1Reserve := c.getPoolReserves(meta, token0AccountIndex, token1AccountIndex, token0Address, token1Address)
						reservesMap[poolAddress] = []string{token0Reserve, token1Reserve}
					}
				}
			}
		}
	}

	var dexSource string
	var foundSource bool
	var tradeResult []helius.SolanaDexTrade
	var newPoolResult []helius.SolanaDexNewPool
	for _, innerInstruction := range innerInstructions {
		for j, instruction := range innerInstruction.Instructions {
			programAccount, _ := meta.ProgramAddress(accountKey, byte(instruction.ProgramIdIndex))
			if !foundSource {
				dexSource, _ = ParserDexSource(programAccount, instruction.Data, instruction.Accounts, accountKey, meta)
				if len(dexSource) > 0 {
					foundSource = true
				}
			}
			if programAccount != consts.MeteoraDLMM {
				continue
			}
			instructionData := base58.Encode(instruction.Data)
			if strings.HasPrefix(instructionData, consts.MeteoraDlmmCreateInstruction) {
				createEvent, _ := meteora.ParseNewPoolEventInstruction(instruction.Data)
				if createEvent != nil {
					token0Address := createEvent.TokenXMint.String()
					token1Address := createEvent.TokenYMint.String()
					newPool := helius.SolanaDexNewPool{
						PairAddress:      createEvent.LBPair.String(),
						DexName:          consts.MeteoraDLMMDex,
						PairName:         fmt.Sprintf("%s-%s", token0Address, token1Address),
						Token0Address:    token0Address,
						Token1Address:    token1Address,
						DeployerAddress:  "",
						CreationTxHash:   signature,
						InstructionIndex: fmt.Sprintf("%d_%d", innerInstruction.Index, j),
						BlockNumber:      uint64(slot),
						CreatedAt:        time.Now().Unix(),
					}
					newPoolResult = append(newPoolResult, newPool)
				}
				continue
			}

			// 匹配交易的话需要考虑前缀
			if strings.HasPrefix(instructionData, consts.MeteoraDlmmTradeInstruction) {
				tradeEvent, _ := meteora.ParseTradeEventInstruction(instruction.Data)
				// get dlmm pool info
				if tradeEvent != nil {
					poolInfo, err := c.poolReader.GetMeteoraDLMMPoolInfo(tradeEvent.LBPair.String())
					if err != nil {
						log.Log.Errorf("Failed to get dlmm pool info: %v", err)
						continue
					}
					var token0Amount, token1Amount decimal.Decimal
					var txType enums.TxType
					token0Address := poolInfo.TokenMint0
					token1Address := poolInfo.TokenMint1
					token0Decimal := splTokenAddresses[poolInfo.TokenMint0].Decimals
					token1Decimal := splTokenAddresses[poolInfo.TokenMint1].Decimals
					if tradeEvent.SwapForY {
						// X -> Y
						if consts.IsChainNativeOrStableCoin(consts.Solana, poolInfo.TokenMint0) {
							txType = enums.TxTypeBuy
						} else {
							txType = enums.TxTypeSell
						}
						token0Amount = utils.ToDecimal(tradeEvent.AmountIn, token0Decimal)
						token1Amount = utils.ToDecimal(tradeEvent.AmountOut, token1Decimal)
					} else {
						// Y -> X
						if consts.IsChainNativeOrStableCoin(consts.Solana, poolInfo.TokenMint1) {
							txType = enums.TxTypeBuy
						} else {
							txType = enums.TxTypeSell
						}
						token1Amount = utils.ToDecimal(tradeEvent.AmountIn, token1Decimal)
						token0Amount = utils.ToDecimal(tradeEvent.AmountOut, token0Decimal)
					}

					var token0UnitPrice decimal.Decimal
					if token1Amount.Cmp(decimal.Zero) > 0 {
						token0UnitPrice = token0Amount.Div(token1Amount).Round(18)
					} else {
						token0UnitPrice = decimal.Zero
					}
					tokenReserves := reservesMap[tradeEvent.LBPair.String()]
					if len(tokenReserves) == 0 {
						tokenReserves = []string{"0", "0"}
					}
					traderAddress, _ := meta.ProgramAddress(accountKey, 0)
					trade := helius.SolanaDexTrade{
						DexName:          consts.MeteoraDLMMDex,
						PoolAddress:      tradeEvent.LBPair.String(),
						TxHash:           signature,
						TxType:           txType,
						Slot:             slot,
						InstructionIndex: fmt.Sprintf("%d_%d", innerInstruction.Index, j),
						Timestamp:        time.Now().Unix(),
						//TraderAddress:    tradeEvent.From.String(),
						TraderAddress:   traderAddress,
						Token0Amount:    token0Amount.String(),
						Token1Amount:    token1Amount.String(),
						Token0UnitPrice: token0UnitPrice.String(),
						Token0Address:   token0Address,
						Token1Address:   token1Address,
						Token0Reserve:   tokenReserves[0],
						Token1Reserve:   tokenReserves[1],
					}
					tradeResult = append(tradeResult, trade)
				}
			}
		}
	}
	// 补充交易来源
	if !foundSource {
		instructions := subscribeUpdateTransaction.Transaction.Transaction.Transaction.Message.Instructions
		for _, instruction := range instructions {
			programAccount, _ := meta.ProgramAddress(accountKey, byte(instruction.ProgramIdIndex))
			dexSource, _ = ParserDexSource(programAccount, instruction.Data, instruction.Accounts, accountKey, meta)
			if len(dexSource) > 0 {
				foundSource = true
				break
			}
		}
	}
	// 填充交易来源
	if foundSource {
		for i := range tradeResult {
			tradeResult[i].DexSource = dexSource
		}
	}

	if tradeResult != nil {
		_ = c.meteoraTradeHandler(tradeResult)
	}

	if newPoolResult != nil {
		_ = c.meteoraNewPoolHandler(newPoolResult)
	}

	return nil
}

type MeteoraDlmmSync struct {
	client *Client
}

func NewMeteoraDlmmSync(grpcAddress string, token string) *MeteoraDlmmSync {
	client, _ := NewClient(grpcAddress, token)
	return &MeteoraDlmmSync{client: client}
}

func (c *MeteoraDlmmSync) Start() {
	c.client.SetDLMMCallback(service.MeteoraService.SaveTrade, service.MeteoraService.SaveNewPool, service.PairService)
	c.client.SubscribeAccount(consts.MeteoraDLMM)
}

func (c *MeteoraDlmmSync) Close() error {
	c.client.Stop()
	return nil
}
